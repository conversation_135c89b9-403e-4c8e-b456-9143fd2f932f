"""
进攻方向箭头SVG生成器

基于A、B、C三点生成战术进攻方向箭头的SVG图形
作者：AI Assistant
版本：1.1 (修改DCE角度为固定值)
"""

import math


def generate_attack_arrow_svg(A, B, C, width=400, height=300, color="#FF0000", stroke_width=2):
    """
    生成进攻方向箭头SVG图形
    
    参数:
        A (tuple): 底边左端点坐标 (x, y)
        B (tuple): 底边右端点坐标 (x, y)  
        C (tuple): 箭头顶点坐标 (x, y)
        width (int): SVG画布宽度，默认400
        height (int): SVG画布高度，默认300
        color (str): 箭头颜色，默认红色 "#FF0000"
        stroke_width (int): 线条粗细，默认2
    
    返回:
        str: SVG格式的字符串
        
    算法说明:
        1. 计算AB中点M。
        2. 计算CM线段上1/3处的O点，O点将作为箭头颈部D、E线段的中点。
        3. 确定D、E点：
           - D、E线段垂直于CM线段。
           - 且D、C、E构成的夹角固定为120度。
           - 利用O点到C点的距离和60度角（120/2），通过三角函数计算O到D（或E）的距离，从而确定D和E。
        4. 计算F、G为DO、EO的中点。
        5. 使用贝塞尔曲线连接形成收敛的箭头形状。
    """
    
    # 计算关键点
    M = ((A[0] + B[0]) / 2, (A[1] + B[1]) / 2)  # AB中点
    
    # O点仍然是CM线段1/3处，作为DE的中点
    # 注意：如果C和M非常近甚至重合，O点可能和C点重合，需要处理这种情况
    CM_vector_from_C = (M[0] - C[0], M[1] - C[1])
    CM_dist_sq = CM_vector_from_C[0]**2 + CM_vector_from_C[1]**2
    
    # 初始化 D, E, O
    D, E, O = (0, 0), (0, 0), (0, 0) # 赋一个默认值以防万一

    if CM_dist_sq < 1e-6: # C和M非常接近，避免除以零或极端情况
        # 此时箭头可能退化为点，D、E、O、C都在一个位置
        O = C
        D = C
        E = C
    else:
        O = (C[0] + CM_vector_from_C[0] / 3, C[1] + CM_vector_from_C[1] / 3)
        
        # 计算 CO 向量和它的长度
        CO_vector = (O[0] - C[0], O[1] - C[1])
        CO_length = math.sqrt(CO_vector[0]**2 + CO_vector[1]**2)
        
        # 定义DCE角度为120度，则直角三角形COD中，角OCD为 120/2 = 60度
        # OD_length = CO_length * tan(60度)
        angle_radians = math.radians(60)
        
        # 检查CO_length是否为零，避免tan计算时CO_length为0导致OD_length也为0
        if CO_length < 1e-6: # O和C非常接近
            OD_length = 0
        else:
            OD_length = CO_length * math.tan(angle_radians)
        
        # 计算垂直于CM（或CO）向量的单位向量
        # CM_vector_from_C (M-C) 指向 O，其垂直向量用于确定 D 和 E
        CM_length = math.sqrt(CM_vector_from_C[0]**2 + CM_vector_from_C[1]**2)

        if CM_length < 1e-6: # C和M点重合，无法定义方向，给一个默认垂直方向
            perp_vector_unit = (0, 1) # 垂直向上的方向
        else:
            # 垂直向量 (y, -x) 或 (-y, x)。这里我们用(-y, x)
            perp_vector_unit = (-CM_vector_from_C[1] / CM_length, CM_vector_from_C[0] / CM_length)

        # 确定 D 和 E 点
        D = (O[0] + perp_vector_unit[0] * OD_length, O[1] + perp_vector_unit[1] * OD_length)
        E = (O[0] - perp_vector_unit[0] * OD_length, O[1] - perp_vector_unit[1] * OD_length) # E点是D点的对称点
    
    # 计算中间点F、G
    F = ((D[0] + O[0]) / 2, (D[1] + O[1]) / 2)
    G = ((E[0] + O[0]) / 2, (E[1] + O[1]) / 2)
    
    # 计算贝塞尔曲线控制点
    # 控制点仍然从A、B向F、G方向延伸一定比例
    # 比例可以根据需要调整，0.7是一个不错的经验值
    ctrl1_ratio = 0.7 
    ctrl2_ratio = 0.7

    AF_vector = (F[0] - A[0], F[1] - A[1])
    BG_vector = (G[0] - B[0], G[1] - B[1])
    ctrl1 = (A[0] + AF_vector[0] * ctrl1_ratio, A[1] + AF_vector[1] * ctrl1_ratio)
    ctrl2 = (B[0] + BG_vector[0] * ctrl2_ratio, B[1] + BG_vector[1] * ctrl2_ratio)
    
    # 计算视图框
    all_points = [A, B, C, D, E, F, G, O, ctrl1, ctrl2]
    all_x = [p[0] for p in all_points]
    all_y = [p[1] for p in all_points]
    padding = 20
    # 确保min/max计算有效，避免空列表
    if not all_x or not all_y:
        min_x, max_x, min_y, max_y = 0, width, 0, height # 默认值
    else:
        min_x, max_x = min(all_x), max(all_x)
        min_y, max_y = min(all_y), max(all_y)

    viewbox = f"{min_x - padding} {min_y - padding} {max_x - min_x + 2 * padding} {max_y - min_y + 2 * padding}"
    
    # 生成SVG
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="{width}" height="{height}" viewBox="{viewbox}">
    <defs>
        <style>
            .attack-arrow {{
                fill: none;
                stroke: {color};
                stroke-width: {stroke_width};
                stroke-linejoin: round;
                stroke-linecap: round;
            }}
        </style>
    </defs>
    <path d="M{A[0]},{A[1]} Q{ctrl1[0]},{ctrl1[1]} {F[0]},{F[1]}" class="attack-arrow"/>
    <path d="M{F[0]},{F[1]}L{D[0]},{D[1]}" class="attack-arrow"/>
    <path d="M{D[0]},{D[1]}L{C[0]},{C[1]}" class="attack-arrow"/>
    <path d="M{C[0]},{C[1]}L{E[0]},{E[1]}" class="attack-arrow"/>
    <path d="M{E[0]},{E[1]}L{G[0]},{G[1]}" class="attack-arrow"/>
    <path d="M{G[0]},{G[1]} Q{ctrl2[0]},{ctrl2[1]} {B[0]},{B[1]}" class="attack-arrow"/>
</svg>'''
    
    return svg_content


def save_svg_file(svg_content, filename):
    """
    保存SVG内容到文件
    
    参数:
        svg_content (str): SVG内容字符串
        filename (str): 保存的文件名
    """
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(svg_content) # 修正：直接写入svg_content
    print(f"SVG文件已保存: {filename}")


def test_arrow_generation():
    """
    测试箭头生成功能
    """
    print("进攻方向箭头生成器测试 (DCE固定120度)")
    print("-" * 30)
    
    # 基本测试用例
    test_cases = [
        {
            "name": "向上箭头",
            "points": ((100, 200), (300, 200), (200, 50)),
            "color": "#FF0000"
        },
        {
            "name": "向右上箭头", 
            "points": ((50, 150), (200, 150), (300, 80)),
            "color": "#0066CC"
        },
        {
            "name": "向左下箭头",
            "points": ((200, 100), (50, 100), (25, 180)),
            "color": "#00AA00"
        },
        {
            "name": "长箭头",
            "points": ((150, 280), (250, 280), (200, 20)),
            "color": "#800080" # 紫色
        },
        {
            "name": "宽箭头",
            "points": ((50, 150), (350, 150), (200, 50)),
            "color": "#FFA500" # 橙色
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        A, B, C = case["points"]
        color = case["color"]
        
        # 生成SVG
        svg = generate_attack_arrow_svg(A, B, C, color=color, stroke_width=3)
        
        # 保存文件
        filename = f"test_arrow_fixed_angle_{i}_{case['name']}.svg"
        save_svg_file(svg, filename)
        
        print(f"✓ {case['name']} 生成完成")
    
    print(f"\n共生成 {len(test_cases)} 个测试箭头")


if __name__ == "__main__":
    # 运行测试
    # test_arrow_generation()
    
    # 单个箭头生成示例
    print("\n" + "=" * 40)
    print("单个箭头生成示例 (DCE固定120度):")
    
    # 定义三个点
    point_A = (100, 250)  # 左下
    point_B = (300, 250)  # 右下
    point_C = (200, 50)   # 顶点
    
    # 生成红色箭头
    arrow_svg = generate_attack_arrow_svg(point_A, point_B, point_C)
    save_svg_file(arrow_svg, "example_attack_arrow_fixed_angle.svg")
    
    print("示例箭头生成完成！")