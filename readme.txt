#
graphrag init --root ./ragtest

graphrag index --root ./ragtest



# 进入项目目录（假设项目部署在/opt/your_project）
cd /opt/your_project

# 创建Python虚拟环境（推荐Python 3.6+）
python3 -m venv venv  # 会在当前目录生成venv文件夹

source venv/bin/activate

# 确保在激活的虚拟环境
(venv) pip install -r requirements.txt

pm2 start ecosystem.config.js



+++++++++++++++
数据库数据迁移：换镜像
docker exec -t 1Panel-postgresql-Cxn7 pg_dump -U postgres -d map-database > map-database_backup.sql


docker run -d --name pg_postgis  -e POSTGRES_PASSWORD=123456 -p 5433:5432  postgis/postgis:17-3.4


docker cp map-database_backup.sql pg_postgis:/map-database_backup.sql


docker exec -it pg_postgis psql -U postgres -c 'CREATE DATABASE "map-database";'


docker exec -i pg_postgis psql -U postgres -d map-database -f /map-database_backup.sql


docker exec -it pg_postgis psql -U postgres -d map-database

CREATE EXTENSION postgis;
SELECT PostGIS_Version();