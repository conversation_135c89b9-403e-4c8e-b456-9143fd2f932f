import os
import json
import re
import sys

import plotly.express as px
import numpy as np
import pandas as pd
from dateutil.parser import parse

from model.llm_provider import LLMProvider


def get_base_path():
    # 判断是否是打包后运行
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    else:
        return os.path.dirname(__file__)

# 配置参数
BASE_DIR = get_base_path()
NEWS_FOLDER = os.path.join(BASE_DIR, 'event_news')
OUTPUT_FILE = os.path.join(BASE_DIR, "event_news_timeline.json")
HTML_FILE = os.path.join(BASE_DIR, "event_news_timeline.html")
llm = LLMProvider().get_client()
DATE_FORMAT = "%Y-%m-%d"


def clear_think_content(text):
    return re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL).strip()


def get_llm_response_with_retry(prompt, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = llm.invoke(prompt).content.strip()
            response = clear_think_content(response)
            return response
        except Exception as e:
            print(f"Retrying due to error: {e}")
            continue
    print("Max retries reached. Exiting.")
    return ''



def extract_timeline_info(title, content):
    """提取时间线和关键事件"""
    #获取当前年份信息
    current_year = str(pd.Timestamp.now().year)
    prompt = f"""
你的任务：请根据给出的国际热点新闻内容，提取出事件的发生时间、发生地点、发生地点经纬度、事件类型、事件内容，要求如下：
1、包含发生时间、发生地点、事件类型、事件内容。
2、发生时间：请使用yyyy-mm-dd格式，如果没有年份信息，请使用{current_year}。
3、发生地点：输出格式为：国家/地区名，城市名。
4、发生地点经纬度：输出格式为：[纬度, 经度]，例如：[38.90, 282.96]。如果涉及多个地点，选择其中事件主动方的、最重要的一个地点，并返回其经纬度
5、事件类型：请严格从以下列表中选择一个事件类型：军事行动、外交动态、经济要闻、政治事件、其他。
6、事件内容：请使用不超过20字的极简概况。

请注意：请严格按照下面的示例格式进行输出！

##示例##：
发生时间：2025-03-31
发生地点：美国，华盛顿
发生地点经纬度：[38.90, 10.96]
事件类型：经济制裁
事件内容：缅甸强震致2886人遇难，中方援助。

新闻内容如下：
新闻标题：{title}
内容：{content[:3000]}"""

    response = get_llm_response_with_retry(prompt)
    print('解析结果：' + response)
    return response



def process_news():
    events = []

    for filename in os.listdir(NEWS_FOLDER):
        if not filename.endswith(".txt"):
            continue

        with open(os.path.join(NEWS_FOLDER, filename), 'r', encoding='utf-8') as f:
            #获取文件路径
            file_path = os.path.abspath(os.path.join(NEWS_FOLDER, filename))
            print(f"正在处理：{filename}")
            title = filename[:-4]
            content = f.read()
            result = extract_timeline_info(title, content)
            print(result)
            parse_result = parse_llm_result(result)
            print(parse_result)
            events.append(parse_result)
    events.sort(key=lambda x: parse(x["time"]))
    return events


def parse_llm_result(result):
    ##按行解析返回结果：
    ##发生时间：2025-02-08
    ##发生地点：中国，哈尔滨
    ##发生地点经纬度：[45.75, 126.63]
    ##事件类型：网络攻击
    ##事件内容：亚冬会期间境外网络攻击设施
    result = result.strip()
    lines = result.split('\n')
    result_dict = {}
    for line in lines:
        if line.startswith("发生时间"):
            result_dict['time'] = line.split("：")[1].strip()
        elif line.startswith("发生地点经纬度"):
            coord = eval(line.split("：")[1].strip())
            log = coord[1]
            if log < 0:
                coord[1] = log + 360
            result_dict['coord'] = coord
        elif line.startswith("发生地点"):
            result_dict['location'] = line.split("：")[1].strip()
        elif line.startswith("事件类型"):
            result_dict['type'] = line.split("：")[1].strip()
        elif line.startswith("事件内容"):
            result_dict['content'] = line.split("：")[1].strip()
    print(result_dict)
    return result_dict


if __name__ == "__main__":
    timeline_data = process_news()

    # 保存结果
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(timeline_data, f, ensure_ascii=False, indent=2)

    print(f"热点事件数量：{len(timeline_data)}条，已保存至{OUTPUT_FILE}")