from langchain_openai import OpenAI
from langchain_openai.chat_models.base import BaseChatOpenAI

from config.settings import ClientConfig, AliyunConfig
from model.QwenClient import ChatQwenClient
from model.azure_client import AzureClient
from model.deepseek_client import DeepseekClient


class LLMProvider:
    def __init__(self):
        self.client = ClientConfig()

    def get_client(self, is_reason=True, speed_first=False):
        if self.client.client_name == "AzureClient":
            return AzureClient.get_llm()
        elif self.client.client_name == "AzureDeepseekClient":
            if is_reason:
                return AzureClient.get_llm_deepseek_reason()
            else:
                return AzureClient.get_llm_deepseek_not_reason()
        elif self.client.client_name == "DeepseekClient":
            return DeepseekClient.get_llm()
        elif self.client.client_name == "DeepseekSiliconFlowClient":
            return DeepseekClient.get_llm_silicon_flow()
        elif self.client.client_name == "DeepseekALIYUNClient":
            return DeepseekClient.get_llm_aliyu()
        elif self.client.client_name == "AliyunQwenClient":
            if speed_first:
                return ChatQwenClient(model_name=AliyunConfig.FAST_MODEL_NAME)
            else:
                return ChatQwenClient(model_name=AliyunConfig.COMPLEX_MODEL_NAME)
        raise ValueError("Unsupported model type")


if __name__ == "__main__":
    # llm = LLMProvider().get_client()
    # result = llm.invoke("你好！")
    # print(result.content)
    # 修改配置文件中的client_name
    ClientConfig.client_name = "AliyunQwenClient"

    llm = LLMProvider().get_client(is_reason=True)
    result = llm.invoke('''
        你是基于qwen2.5的还是qwen3的模型，你是推理模型还是非推理模型。我在api中如何关闭掉推理，我只想让你普通生成，不需要推理
        
    ''')
    print(result.content)
