from langchain_azure_ai.chat_models import AzureAIChatCompletionsModel
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings
from typing import List
from config.settings import AzureConfig


class AzureClient:
    @classmethod
    def get_llm(cls):
        return AzureChatOpenAI(
            deployment_name=AzureConfig.DEPLOYMENT_NAME,
            azure_endpoint=AzureConfig.ENDPOINT,
            openai_api_key=AzureConfig.API_KEY,
            openai_api_version=AzureConfig.API_VERSION,
            temperature=0.2,
            max_tokens=3600,
            top_p=0.9,
            frequency_penalty=0.8,
            presence_penalty=0.6
        )

    @classmethod
    def get_llm_deepseek_reason(cls):
        return AzureAIChatCompletionsModel(
            endpoint=AzureConfig.ENDPOINT_DEEPSEEK,
            credential=AzureConfig.API_KEY,
            model_name='DeepSeek-R1',
            api_version=AzureConfig.API_VERSION_DEEPSEEK
        )

    @classmethod
    def get_llm_deepseek_not_reason(cls):
        return AzureAIChatCompletionsModel(
            endpoint=AzureConfig.ENDPOINT_DEEPSEEK,
            credential=AzureConfig.API_KEY,
            model_name=AzureConfig.MODEL_NAME_DEEPSEEK,
            api_version=AzureConfig.API_VERSION_DEEPSEEK
        )
    @classmethod
    def get_embedding_client(cls) -> AzureOpenAIEmbeddings:
        """获取官方Azure嵌入客户端"""
        return AzureOpenAIEmbeddings(
            azure_deployment=AzureConfig.EMBEDDING_DEPLOYMENT_NAME,
            openai_api_version=AzureConfig.EMBEDDING_API_VERSION,
            azure_endpoint=AzureConfig.EMBEDDING_ENDPOINT,
            api_key=AzureConfig.API_KEY,
        )

    @classmethod
    def get_embeddings(cls, texts: List[str]) -> List[List[float]]:
        """批量生成嵌入向量（推荐使用）"""
        return cls.get_embedding_client().embed_documents(texts)

    @classmethod
    def get_embedding(cls, text: str) -> List[float]:
        """生成单个文本的嵌入向量"""
        return cls.get_embedding_client().embed_query(text)


if __name__ == "__main__":
    llm = AzureClient.get_llm_deepseek_reason()
    result = llm.invoke("你好")
    print(result.content)
