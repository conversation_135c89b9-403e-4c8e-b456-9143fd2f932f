from langchain_openai.chat_models.base import BaseChatOpenAI
from langchain_core.prompts.chat import Chat<PERSON>romptTemplate
from config.settings import DeepseekConfig


class DeepseekClient:
    @classmethod
    def get_llm(cls):
        return BaseChatOpenAI(
            # model='deepseek-reasoner',
            model=DeepseekConfig.DEPLOYMENT,
            openai_api_key=DeepseekConfig.API_KEY,
            openai_api_base='https://api.deepseek.com',
            # max_tokens=1024
        )

    @classmethod
    def get_llm_silicon_flow(cls):
        return BaseChatOpenAI(
            model=DeepseekConfig.DEPLOYMENT_SILICON,
            # model='deepseek-ai/DeepSeek-V3',
            # model='deepseek-ai/DeepSeek-R1-Distill-Qwen-32B',
            # model='deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
            openai_api_key=DeepseekConfig.API_KEY_SILICON,
            openai_api_base='https://api.siliconflow.cn'
        )

    @classmethod
    def get_llm_aliyu(cls):
        return BaseChatOpenAI(
            model=DeepseekConfig.DEPLOYMENT_ALIYUN,
            # model='deepseek-v3',
            # model='deepseek-r1-distill-qwen-7b',
            # model='deepseek-r1-distill-qwen-32b',
            openai_api_key=DeepseekConfig.API_KEY_ALIYUN,
            openai_api_base='https://dashscope.aliyuncs.com/compatible-mode/v1',
            temperature=0.4,
            max_tokens=3600,
            top_p=0.9,
            frequency_penalty=0.8,
            presence_penalty=0.6
        )


if __name__ == "__main__":
    llm = DeepseekClient.get_llm_aliyu()
    response = llm.invoke("你好!")
    print(response.content)

    # template_multiple = """请为下面的主题生成一篇文章的大纲：{topic}。
    # 编写要求包括：{outline_point}，请使用markdown格式输出。"""
    # prompt_multiple = ChatPromptTemplate.from_template(template_multiple)
    # chain = prompt_multiple | llm
    # response = chain.invoke({"topic": "美国重点国防关键技术发展预测研究",
    #                          "outline_point": "梳理美国重点发展的国防关键技术，并对超大型无人潜航器技术、水下高速安全通信技术、高超声速技术、海上无人系统集群技术，深入研究其发展现状、典型项目、运用情况、发展趋势等，并分析研判其技术水平。编写风格为专业咨询文件，注重事实，不要编造。"})
    # print(response.content)
