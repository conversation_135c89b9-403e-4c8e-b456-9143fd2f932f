import requests
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.outputs import ChatResult, ChatGeneration
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from pydantic import Field
from typing import Any, Dict, List, Optional
from config.settings import AliyunConfig


class ChatQwenClient(BaseChatModel):
    """LangChain 兼容的通义千问客户端"""

    api_key: str = AliyunConfig.API_KEY
    endpoint: str = AliyunConfig.API_BASE
    model_name: str = AliyunConfig.COMPLEX_MODEL_NAME

    def _generate(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            **kwargs: Any,
    ) -> ChatResult:
        # 构建符合阿里云API格式的消息
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, HumanMessage):
                formatted_messages.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                formatted_messages.append({"role": "assistant", "content": msg.content})

        payload = {
            "model": self.model_name,
            "input": {
                "messages": formatted_messages
            },
            "parameters": {
                "temperature": 0.5,
                'enable_thinking': False,
                **kwargs.get("parameters", {})
            }
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        response = requests.post(self.endpoint, json=payload, headers=headers, timeout=180)
        response.raise_for_status()
        result = response.json()
        # print(self.model_name)
        # print("完整API响应:", result)  # 新增此行

        # 提取生成的文本
        output_text = result.get("output", {}).get("text", "")
        ai_message = AIMessage(content=output_text)
        # 包装为 ChatGeneration
        generation = ChatGeneration(message=ai_message)  # ✅ 正确类型
        # 返回 ChatResult
        return ChatResult(generations=[generation])

    @property
    def _llm_type(self) -> str:
        """返回LLM类型标识"""
        return "qwen-client"
