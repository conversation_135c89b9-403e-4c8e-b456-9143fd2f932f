import concurrent
import datetime
import json
import traceback
from typing import Any, Dict
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate

from langgraph_agent.agent_state import AgentState, TaskType, MainTaskStatus, \
    SemanticSubDecomposition, SemanticSubTask, MapParseParameters, TaskTypeEnum, SubTaskTypeEnum, SubTaskDataCondition
from service_api.get_draw_data_service import GetDrawDataService
from service_api.icon_service import IconService
from service_api.knowledge_service import KnowledgeService
from tool.llm_tool import llm_not_reason


def langgraph_agent_meta_data_create_node(state: AgentState):
    """
    创建元数据节点，仅补充缺失字段
    """
    time_stamp_str = datetime.datetime.now().strftime("%Y%m%d%H%M%S%f")
    if not state.get("session_id"):
        state["session_id"] = time_stamp_str
    if not state.get("timestamp"):
        state["timestamp"] = time_stamp_str
    if not state.get("status"):
        state["status"] = "init"

    return state

def get_task_type_node(state: AgentState):
    task_template = """请判断用户输入的任务类型：
    {user_input}

    必须严格从以下选项中选择：
    {task_options}
    
    请注意：户的输入可以在地图上标绘，则认为是该类型，网络攻击相关的也属于地图标绘
    
    返回格式：
    {{"task_type": "选择的任务类型"}}"""

    prompt = PromptTemplate(
        template=task_template,
        input_variables=["user_input"],
        partial_variables={
            "task_options": "、".join(
                TaskType.schema()["properties"]["task_type"]["enum"]
            )
        }
    )

    # 3. 创建解析链
    parser = JsonOutputParser(pydantic_object=TaskType)

    # 使用示例
    chain = prompt | llm_not_reason | parser

    # 测试运行
    result = chain.invoke({"user_input": state['user_input']})
    print(result)
    task_logs = state.get("task_logs")
    # 类型校验逻辑
    if result['task_type'] not in [TaskTypeEnum.MAP_PLOT]:  # 只允许地图标绘类型
        print("任务类型校验失败：不支持的任务类型")
        error_msg = "任务类型校验失败：不支持的任务类型"
        task_logs.append(error_msg)
        state["status"] = MainTaskStatus.FAILED.value
        state["error_messages"] = [error_msg]
        return {'status': MainTaskStatus.FAILED.value, 'error_messages': [error_msg], 'task_logs': task_logs}
    task_logs.append(f"✅ 任务类型校验成功")
    return {'main_task_type': result['task_type'], 'status': MainTaskStatus.RUNNING.value, 'task_logs': task_logs}

def task_decompose_node(state: AgentState):
    task_logs = state.get("task_logs")
    task_logs.append(f"开始任务分解...")
    # 定义解析器
    parser = JsonOutputParser(pydantic_object=SemanticSubDecomposition)

    knowledge_info = KnowledgeService.get_knowledge_info_str()

    decompose_prompt = PromptTemplate(
        template="""请根据用户的输入，返回解析后一个或多个地图标绘子任务列表，请注意：事件类标绘为一个子任务，不必要再将事件类任务再进行细分。
            用户输入：{user_input}
            
            知识库信息：{knowledge_info}
            
            要求：
            1. 每个子任务必须包含 task_id、sub_task_type、title、description、data_source、is_china、dependencies
            2. 使用 JSON 格式输出，严格遵循 schema：{schema}

            示例输出：
            {{
                "subtasks": [
                    {{
                        "task_id": "task_1",
                        "sub_task_type": "静态目标标绘",
                        "title": "中国机场数据",
                        "description": "中国机场数据标绘",
                        "data_source": "机场知识库",
                        "is_china": True,
                        "dependencies": []
                    }}
                ]
            }}
            """,
        input_variables=["user_input", "main_task_type"],
        partial_variables={
            "schema": parser.get_format_instructions(),
            "knowledge_info": knowledge_info
        }
    )

    # 构建调用链
    chain = decompose_prompt | llm_not_reason | parser

    # 调用 LLM 分解任务
    result = chain.invoke({
        "user_input": state["user_input"],
        "main_task_type": state.get("main_task_type", "")
    })
    print(result)
    task_logs.append(f"任务分解完成，共分解出 {len(result['subtasks'])} 个子任务")
    task_descriptions = [task["description"] for task in result["subtasks"]]
    task_logs.append(f"子任务列表：{'; '.join(task_descriptions)}")
    task_logs.append(f"✅ 任务分解成功")
    task_logs.append(f"开始子任务校验...")
    invalid_tasks = [
        task for task in result["subtasks"]
        if task["sub_task_type"] == SubTaskTypeEnum.OTHER.value or task['data_source'] == '无'
    ]
    if invalid_tasks:
        error_msgs = []
        state["error_messages"] = error_msgs
        for task in invalid_tasks:
            if task["sub_task_type"] == SubTaskTypeEnum.OTHER.value:
                print("任务类型校验失败：不支持的任务类型")
                error_msg = f"任务类型校验失败：{task['description']} 不支持的任务类型"
                task_logs.append(error_msg)
                state["status"] = MainTaskStatus.FAILED.value
                state["error_messages"].append(error_msg)
            if task['data_source'] == '无':
                print("任务类型校验失败：暂无数据源")
                error_msg = f"任务类型校验失败：{task['description']} 暂无数据源"
                task_logs.append(error_msg)
                state["status"] = MainTaskStatus.FAILED.value
                state["error_messages"].append(error_msg)
        task_logs.append(f"子任务校验失败，共 {len(invalid_tasks)} 个子任务不符合要求")
        return {
            'semantic_subtasks': result['subtasks'],
            'status': MainTaskStatus.FAILED.value,
            'error_messages': error_msgs,
            'task_logs': task_logs
        }
    return {"semantic_subtasks": result["subtasks"], "task_logs": task_logs}


def execute_semantic_subtask_function(semantic_subtask_dict: Dict[str, Any], task_logs: [str]) -> Dict[str, Any]:
    time_str = datetime.datetime.now().strftime("%H:%M:%S")
    task_logs.append(f"{time_str} 开始执行子任务：{semantic_subtask_dict['description']}")
    semantic_subtask = SemanticSubTask(**semantic_subtask_dict)
    subtask_execute_result = {}
    data_condition = get_subtask_draw_data_condition(semantic_subtask)
    draw_data = get_subtask_draw_data(semantic_subtask_dict, data_condition, task_logs)
    task_logs.append(f"子任务：{semantic_subtask_dict['description']} 数据 获取完成，共{len(draw_data)}条数据")
    icon_result = get_subtask_icon_result(semantic_subtask)
    task_logs.append(f"子任务：{semantic_subtask_dict['description']} 图标 获取完成")
    for data in draw_data:
        data['icon_path'] = icon_result
    subtask_execute_result['数据选择条件'] = data_condition
    subtask_execute_result['图标路径'] = icon_result
    subtask_execute_result['标绘数据'] = draw_data
    return subtask_execute_result


def get_subtask_draw_data_condition(semantic_subtask: SemanticSubTask) -> Dict[str, Any]:
    """
    使用大模型解析子任务的数据过滤条件
    Args:
        semantic_subtask: 语义子任务对象
    Returns:
        解析后的数据过滤条件对象
    """
    # 定义提示词模板
    condition_prompt = PromptTemplate(
        template="""
        你是一位出色的数据分析员，用户需要从{data_source}中获取数据。请从以下子任务描述中提取准确的数据过滤条件：
        子任务类型："{sub_task_type}"
        子任务描述："{description}"

        要求：
        1. 需求提取的是数据过滤条件，不需要和数据过滤无关的内容，比如底图要求
        2. 空间范围为必填项
        3. 数据条数应为整数
        4. 如果描述中包含“X 天/周/月内”或“X 内”等表达，请统一解析为“最近 X 天/周/月”。
           - 例如：“一周内” → “最近7天”
           - “三个月内” → “最近90天”
           - “一天内” → “最近1天”

        返回符合schema的JSON对象：
        {schema}
        """,
        input_variables=["data_source", "description", "sub_task_type"],
        partial_variables={
            "schema": JsonOutputParser(pydantic_object=SubTaskDataCondition).get_format_instructions()
        }
    )

    # 创建解析链
    parser = JsonOutputParser(pydantic_object=SubTaskDataCondition)
    chain = condition_prompt | llm_not_reason | parser

    # 调用大模型解析
    result = chain.invoke({
        "data_source": semantic_subtask.data_source,
        "description": semantic_subtask.description,
        "sub_task_type": semantic_subtask.sub_task_type
    })
    print(result)
    # 返回SubTaskDataCondition对象
    data_condition = SubTaskDataCondition(**result)
    useful_condition = {}

    if data_condition.time_scope_condition not in ["无", ""]:
        useful_condition['时间范围'] = data_condition.time_scope_condition
    if data_condition.area_condition not in ["无", ""]:
        useful_condition['空间范围'] = data_condition.area_condition
    if data_condition.other_condition not in ["无", ""]:
        useful_condition['其他条件'] = data_condition.other_condition
    if data_condition.data_count_condition > 0:
        useful_condition['数据条数'] = data_condition.data_count_condition
    else:
        if semantic_subtask.sub_task_type == SubTaskTypeEnum.NEWS_PLOT.value:
            useful_condition['数据条数'] = 10

    # if semantic_subtask.sub_task_type == SubTaskTypeEnum.STATIC_OBJECT_PLOT.value:
    #     useful_condition['空间范围'] = data_condition.area_condition
    #     useful_condition['其他条件'] = data_condition.other_condition
    # else:
    #     useful_condition['时间范围'] = data_condition.time_scope_condition
    #     useful_condition['数据条数'] = data_condition.data_count_condition
    #     useful_condition['空间范围'] = data_condition.area_condition
    #     useful_condition['其他条件'] = data_condition.other_condition
    print(useful_condition)
    return useful_condition

def get_subtask_draw_data(semantic_subtask_dict: Dict[str, Any], data_condition: Dict[str, Any], task_logs: [str]):
    return_data = GetDrawDataService.get_draw_data(semantic_subtask_dict, data_condition, task_logs)
    if type(return_data) == str:
        return json.loads(return_data)
    else:
        return return_data

def get_subtask_icon_result(semantic_subtask: SemanticSubTask):
    data_source_name = semantic_subtask.data_source
    subtask_description = semantic_subtask.description
    icon_related_info = f"标绘内容：{subtask_description}，数据源：{data_source_name}"
    icon_path = IconService.get_icon_path_from_semantic_task_info(icon_related_info)
    print(icon_path)
    return icon_path


def subtasks_execute_node(state: AgentState):
    task_logs = state.get("task_logs", [])
    def execute_task(semantic_subtask_dict: Dict[str, Any], task_logs: [str]):
        try:
            result = execute_semantic_subtask_function(semantic_subtask_dict, task_logs)
        except Exception as e:
            # 打印异常堆栈
            traceback.print_exc()
            result = {"error": str(e)}
        return semantic_subtask_dict['task_id'], result, task_logs

    task_results = {}
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = [executor.submit(execute_task, task, task_logs) for task in state.get("semantic_subtasks", [])]
        for future in concurrent.futures.as_completed(futures):
            task_id, result, task_logs = future.result()
            task_results[task_id] = result

    return {"semantic_subtask_results": task_results, "task_logs": task_logs}


def task_aggregation_node(state: AgentState):
    task_results = state.get("semantic_subtask_results", {})
    completed = [k for k, v in task_results.items() if "error" not in v]
    failed = [k for k, v in task_results.items() if "error" in v]
    task_logs = state.get("task_logs", [])
    task_logs.append(f"任务聚合完成：{len(completed)} 个任务完成，{len(failed)} 个任务失败")
    final_result = {
        "results": task_results,
        "completed_tasks": completed,
        "failed_tasks": failed,
        "message": f"{len(completed)} 个任务完成，{len(failed)} 个任务失败"
    }

    return {
        "final_result": final_result,
        "status": MainTaskStatus.COMPLETED.value if completed else MainTaskStatus.FAILED.value,
        "task_logs": task_logs
    }


def map_parameters_node(state: AgentState):
    semantic_subtasks = state.get("semantic_subtasks", [])
    semantic_subtask_results = state.get("semantic_subtask_results", [])
    task_logs = state.get("task_logs", [])
    task_logs.append("开始生成地图参数")
    parser = JsonOutputParser(pydantic_object=MapParseParameters)
    subtask_map_infos = ''
    for task in semantic_subtasks:
        task_id = task['task_id']
        task_description = task['description']
        task_result = semantic_subtask_results.get(task_id, {})
        task_space = task_result.get('数据选择条件', {}).get('空间范围', '')
        subtask_map_infos += f"""
        子任务信息：
        --子任务ID：{task_id}
        --子任务描述：{task_description}
        --子任务空间范围：{task_space}
        
"""
    # 构建提示词模板
    map_params_prompt = PromptTemplate(
        template="""请根据以下子任务信息生成最终的地图解析参数：
                子任务信息：
                {subtask_info}
                
                可选底图列表：
                底图名称：网络攻击底图        适合场景：网络攻击
                底图名称：事件标绘底图        适合场景：全球、事件标绘
                底图名称：中国标绘底图        适合场景：中国、事件标绘
                底图名称：影像底图        适合场景：全球、影像场景

                要求：
                1. 生成符合 schema 的 JSON 对象，不需要任何解释和说明。
                2. 如果存在空间范围为全球的子任务，则返回的map_space_name为全球，map_center为[155, 15],map_zoom为3
                3. 最终解析的地图参数，需保证各个子任务都可以在地图上进行很好的标绘
                4. 如果子任务描述中有要求底图/地图样式，则按照要求内容从可选底图列表中选择最合适的底图名称，请注意，底图名称必须严格来自给出的底图名称，不得修改。

                返回格式：
                {{"map_title": "...", "map_name": "...", "map_space_name": "...", "map_center": [lng, lat], "map_zoom": 10}}
                """,
        input_variables=["subtask_info"],
        partial_variables={"schema": parser.get_format_instructions()}
    )

    # 构建调用链
    chain = map_params_prompt | llm_not_reason | parser

    # 调用 LLM 生成参数
    result = chain.invoke({"subtask_info": subtask_map_infos})
    print("生成的地图参数：", result)
    task_logs.append(f"生成的地图参数完成")
    return {"map_parameters": result, "task_logs": task_logs}


def organize_ui_data_node(state: AgentState):
    ui_final_result = {'sub_tasks_result': [], 'TYPE': state["main_task_type"]}

    task_logs = state.get("task_logs")
    task_logs.append("开始进行数据整理...")
    map_parameters = state.get("map_parameters", {})
    ui_final_result['MAP'] = map_parameters.get('map_name', '')
    ui_final_result['SPACE'] = map_parameters.get('map_space_name', '')
    ui_final_result['MAP_CENTER'] = map_parameters.get('map_center', [])
    ui_final_result['TITLE'] = map_parameters.get('map_title', '')
    ui_final_result['ZOOM'] = map_parameters.get('map_zoom', 4)
    semantic_subtasks = state.get("semantic_subtasks", [])
    semantic_subtask_results = state.get("semantic_subtask_results", [])
    for sub_task in semantic_subtasks:
        task_id = sub_task['task_id']
        sub_task_result = {}
        task_result = semantic_subtask_results.get(task_id, {})
        sub_task_result['TYPE'] = sub_task['sub_task_type']
        if sub_task['sub_task_type'] == '网络攻击':
            ui_final_result['TYPE'] = '网络攻击'
        sub_task_result['IS_CHINA'] = sub_task['is_china']
        sub_task_result['DRAW_DATA'] = task_result['标绘数据']
        sub_task_result['TITLE'] = sub_task['title']
        ui_final_result['sub_tasks_result'].append(sub_task_result)

    if ui_final_result['SPACE'] == '全球':
        for sub_task_result in ui_final_result['sub_tasks_result']:
            draw_data = sub_task_result['DRAW_DATA']
            if draw_data:
                for data in draw_data:
                    coordinates = data.get('coord', [])
                    if coordinates:
                        log = coordinates[1]
                        if log < -20:
                            coordinates[1] = log + 360

    task_logs.append("数据整理完成")
    return {"ui_final_result": ui_final_result, "task_logs": task_logs}

def handle_error_node(state: AgentState):
    """错误处理节点"""
    error_msg = state.get("error_messages", ["未知错误"])[-1]
    state["task_logs"].append(f"流程终止：{error_msg}")
    return state

