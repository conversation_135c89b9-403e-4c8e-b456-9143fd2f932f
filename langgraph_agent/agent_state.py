from enum import Enum
from typing import TypedDict, List, Optional, Any, Dict
from pydantic.v1 import BaseModel, Field


class SubTaskDataCondition(BaseModel):
    time_scope_condition: Optional[str] = Field(description="时间范围，标注内容的时间范围，比如最近一周、最近三天、2023-01-01 到 2023-01-07 等，默认值为无。")
    data_count_condition: Optional[int] = Field(description="数据条数，事件类标绘任务必填项，如：10，默认值为0")
    area_condition: str = Field(description="空间范围，必填项，表示需要标绘内容所在的地理空间范围，如全球，欧洲，中国陕西省等，默认值为全球")
    other_condition: Optional[str] = Field(description="其他和数据过滤相关的条件，如果有多个值，则按中文分号连接，如：大型机场；民用机场。默认值为无")

class MapParseParameters(BaseModel):
    map_title: str = Field(description="地图标题")
    map_name: str = Field(description="底图名称，严格从给出的底图名称列表中返回对应的地图名称")
    # map_corners: List[float] = Field(description="地图角点坐标，格式为 [lng1, lat1, lng2, lat2]，其中‌lng1, lat1‌：表示地形图左上角的经纬度坐标；‌lng2, lat2‌：表示地形图右下角的经纬度坐标")
    map_space_name: str = Field(description="地图空间范围名称，表示地图显示的空间范围，默认为全球。")
    map_center: List[float] = Field(description="地图中心坐标，格式为 [lng, lat]，其中 lng：表示地图中心点的经度；lat：表示地图中心点的纬度")
    map_zoom: int = Field(description="地图缩放级别，范围从3到18，数值越大，地图显示越详细")

class TaskTypeEnum(str, Enum):
    MAP_PLOT = "地图标绘"
    OTHER = "其他"


class TaskType(BaseModel):
    task_type: TaskTypeEnum = Field(
        description="必须从以下选项选择",
        enum=[TaskTypeEnum.MAP_PLOT, TaskTypeEnum.OTHER],
        example="地图标绘",
        title="任务类型",
        json_schema_extra={
            "description": {
                "地图标绘": "用户的输入可以在地图上标绘，则认为是该类型，网络攻击相关的也属于地图标绘",
                "其他": "和地图和网络攻击完全无关"
            }
        }
    )

class MainTaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TERMINATED = "terminated"
    CANCELED = "canceled"
    INIT = "init"

class SubTaskTypeEnum(str, Enum):
    NEWS_PLOT = "事件类标绘"
    STATIC_OBJECT_PLOT = "静态目标标绘"
    NET_ATTACK_PLOT = "网络攻击"
    OTHER = "其他"

class SemanticSubTask(BaseModel):
    """语义子任务结构（供LLM输出解析）"""
    task_id: str = Field(description="必填，子任务唯一标识（如 task_1）")
    sub_task_type: SubTaskTypeEnum = Field(
        description="必填，子任务类型（如 事件类标绘）",
        enum=[SubTaskTypeEnum.NEWS_PLOT, SubTaskTypeEnum.STATIC_OBJECT_PLOT, SubTaskTypeEnum.NET_ATTACK_PLOT,
              SubTaskTypeEnum.OTHER],
        title="子任务类型",
        json_schema_extra={
            "description": {
                "事件类标绘": "涉及新闻、热点、突发事件等动态事件的标绘任务，如'全球热点事件标绘'",
                "静态目标标绘": "涉及固定目标的标绘任务，如机场、港口、水电站、地震等数据，请注意地震数据标绘也是属于静态目标描绘",
                "网络攻击": "涉及网络攻击、网络攻防、网络安全事件的任务，如'DDoS攻击事件分析'",
                "其他": "不属于以上类型的其他任务"
            }
        }
    )
    title: str = Field(description="必填，子任务标题，指的是标绘的内容（如 全球热点事件，美国机场数据）")
    description: str = Field(description='''
必填，任务描述以及标绘要求。
请遵循：
 - 完全忠实于用户原始输入，不增不减。
 - 将每个要求单独列出。
 - 保留原始表述中的关键词语。''')
    data_source: str = Field(description="必填，根据给出的知识库信息，返回对应的知识库名称（如 新闻事件知识库、机场知识库、港口知识库等）。如果没有合适的可选知识库，返回无")
    is_china: bool = Field(description="必填，是否为中国数据", default=False)
    dependencies: List[str] = Field(description="必填，依赖的其他任务ID", default=[])

class SemanticSubDecomposition(BaseModel):
    """任务分解结果（包含子任务列表）"""
    subtasks: List[SemanticSubTask] = Field(description="分解后的子任务列表")

class SubTask(TypedDict):
    """子任务定义"""
    task_id: str
    task_type: str
    description: str
    parameters: Dict[str, Any]
    status: str  # "pending", "running", "completed", "failed"
    result: Optional[Any]
    dependencies: List[str]  # 依赖的其他子任务ID
    can_terminate_early: bool  # 是否可以提前终止并输出结果


class AgentState(TypedDict, total=False):
    # 用户输入和基本信息
    user_input: str
    session_id: str
    timestamp: str
    status: str

    # 任务路由信息
    main_task_type: str
    # 当前执行node名称
    current_node: Optional[str]

    # 任务分解结果
    semantic_subtasks: List[SemanticSubTask]  # 语义子任务列表
    semantic_subtask_results: Dict[str, Any]  # 语义子任务结果
    ui_final_result: Optional[Dict[str, Any]]  # UI最终结果
    map_parameters: Optional[MapParseParameters]  # 地图解析参数
    subtasks: List[SubTask]
    current_subtask_id: Optional[str]
    completed_subtasks: List[str]
    failed_subtasks: List[str]

    # 地图制图相关状态
    map_requirements: Optional[Dict[str, Any]]  # 地图需求分析结果
    collected_data: Dict[str, Any]  # 按数据源分类的收集数据
    filtered_data: Dict[str, Any]  # 筛选后的数据
    map_config: Optional[Dict[str, Any]]  # 地图配置信息
    plotting_results: List[Dict[str, Any]]  # 多个标绘结果

    # 分析相关状态
    analysis_requirements: Optional[Dict[str, Any]]
    analysis_results: List[Dict[str, Any]]

    # 报告生成相关状态（预留）
    report_requirements: Optional[Dict[str, Any]]
    report_content: Optional[Dict[str, Any]]

    # 执行控制
    can_terminate_now: bool  # 当前是否可以终止并输出结果
    termination_reason: Optional[str]  # 终止原因

    # 输出结果
    intermediate_results: List[Dict[str, Any]]  # 中间结果
    final_result: Optional[Dict[str, Any]]

    # 元数据和日志
    task_logs: List[str]
    execution_path: List[str]  # 执行路径记录
    error_messages: List[str]
    performance_metrics: Dict[str, Any]