import datetime
from typing import Dict, Any, List
from langgraph.graph import StateGraph, END

from langgraph_agent.agent_nodes.agent_nodes_define import langgraph_agent_meta_data_create_node, get_task_type_node, \
    task_decompose_node, subtasks_execute_node, task_aggregation_node, map_parameters_node, organize_ui_data_node, \
    handle_error_node
from langgraph_agent.agent_state import AgentState, MainTaskStatus, TaskTypeEnum





def create_main_graph() -> StateGraph:
    """创建主要的LangGraph工作流"""

    # 创建状态图
    graph = StateGraph(AgentState)

    # 添加节点
    graph.add_node("langgraph_agent_meta_data_create_node", langgraph_agent_meta_data_create_node)
    graph.add_node("get_task_type_node", get_task_type_node)
    graph.add_node("task_decompose_node", task_decompose_node)
    graph.add_node("subtasks_execute_node", subtasks_execute_node)
    graph.add_node("task_aggregation_node", task_aggregation_node)
    graph.add_node("map_parameters_node", map_parameters_node)
    graph.add_node("organize_ui_data_node", organize_ui_data_node)
    graph.add_node("handle_error_node", handle_error_node)

    # 设置入口点
    graph.set_entry_point("langgraph_agent_meta_data_create_node")

    graph.add_edge("langgraph_agent_meta_data_create_node", "get_task_type_node")

    # 添加条件边
    graph.add_conditional_edges(
        "get_task_type_node",
        lambda state: state["status"],
        {
            MainTaskStatus.FAILED.value: "handle_error_node",
            MainTaskStatus.RUNNING.value: "task_decompose_node"
        }
    )

    graph.add_conditional_edges(
        "task_decompose_node",
        lambda state: state["status"],
        {
            MainTaskStatus.FAILED.value: "handle_error_node",
            MainTaskStatus.RUNNING.value: "subtasks_execute_node"
        }
    )

    graph.add_edge("subtasks_execute_node", "task_aggregation_node")
    graph.add_edge("task_aggregation_node", "map_parameters_node")
    graph.add_edge("map_parameters_node", "organize_ui_data_node")
    graph.add_edge("organize_ui_data_node", END)
    graph.add_edge("handle_error_node", END)
    # 编译图
    graph = graph.compile()
    return graph


def create_initial_state(user_input: str, task_logs: List[str]) -> AgentState:
    """
    构建初始状态，避免在 invoke() 中重复初始化
    """
    time_stamp_str = datetime.datetime.now().strftime("%Y%m%d%H%M%S%f")
    return {
        "user_input": user_input,
        "session_id": time_stamp_str,
        "timestamp": time_stamp_str,
        "status": "init",
        "task_logs": task_logs or [],
        "execution_path": [],
        "error_messages": []
    }


class LangGraphAgent:
    """LangGraph智能体主类"""

    def __init__(self):
        self.graph = create_main_graph()

    def invoke(self, user_input: str, task_logs: [str]) -> Dict[str, Any]:
        """执行智能体任务"""
        # 初始化状态
        initial_state = create_initial_state(user_input, task_logs)
        # 执行图
        result = self.graph.invoke(initial_state)

        return result



if __name__ == '__main__':
    agent = LangGraphAgent()
    result = agent.invoke('中国台湾省港口，要求在影像地图上进行标绘', [])
    print(result)