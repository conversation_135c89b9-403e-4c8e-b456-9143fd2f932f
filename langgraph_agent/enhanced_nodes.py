"""
增强的LangGraph节点，集成语义向量库功能
包括动态Prompt生成、智能工具选择和符号匹配
"""

from typing import Dict, Any, List
import json
from langgraph_agent.agent_state import AgentState, TaskType, TaskStatus
from tool.dynamic_prompt_generator import DynamicPromptGenerator, PromptContext, PromptLevel
from tool.intelligent_tool_selector import IntelligentToolSelector, TaskRequirement, ToolCategory
from tool.intelligent_symbol_matcher import IntelligentSymbolMatcher, PlottingRequest, SymbolCategory
from model.llm_provider import LLMProvider


class EnhancedTaskDecompositionNode:
    """增强的任务分解节点"""
    
    def __init__(self):
        self.prompt_generator = DynamicPromptGenerator()
        self.llm_provider = LLMProvider()
    
    def __call__(self, state: AgentState) -> AgentState:
        """执行任务分解"""
        try:
            # 构建Prompt上下文
            context = PromptContext(
                task_type=state.get("task_type", "未知任务"),
                user_input=state.get("work_content", ""),
                current_step="任务分解",
                available_tools=state.get("available_tools", []),
                data_sources=state.get("data_sources", []),
                constraints=state.get("constraints", []),
                examples=state.get("examples", []),
                domain_knowledge=state.get("domain_knowledge", [])
            )
            
            # 动态生成任务分解Prompt
            decomposition_prompt = self.prompt_generator.generate_prompt(
                prompt_type="task_decomposition",
                context=context,
                target_level=PromptLevel.LEVEL_2
            )
            
            # 调用LLM进行任务分解
            llm_client = self.llm_provider.get_client(is_reason=True)
            response = llm_client.chat_completion(
                messages=[{"role": "user", "content": decomposition_prompt}],
                temperature=0.1
            )
            
            # 解析分解结果
            decomposition_result = self._parse_decomposition_result(response)
            
            # 更新状态
            state["subtasks"] = decomposition_result.get("subtasks", [])
            state["task_status"] = TaskStatus.DECOMPOSED.value
            state["current_step"] = "任务分解完成"
            
            return state
            
        except Exception as e:
            state["error"] = f"任务分解失败: {str(e)}"
            state["task_status"] = TaskStatus.ERROR.value
            return state
    
    def _parse_decomposition_result(self, response: str) -> Dict[str, Any]:
        """解析任务分解结果"""
        try:
            # 尝试解析JSON格式的响应
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_content = response[json_start:json_end].strip()
                return json.loads(json_content)
            else:
                return json.loads(response)
        except json.JSONDecodeError:
            # 如果JSON解析失败，返回基本结构
            return {
                "subtasks": [
                    {
                        "id": "task_1",
                        "name": "数据处理",
                        "description": response[:200] + "...",
                        "dependencies": [],
                        "estimated_time": "未知",
                        "required_tools": []
                    }
                ]
            }


class EnhancedToolSelectionNode:
    """增强的工具选择节点"""
    
    def __init__(self):
        self.tool_selector = IntelligentToolSelector()
        self.prompt_generator = DynamicPromptGenerator()
        self.llm_provider = LLMProvider()
    
    def __call__(self, state: AgentState) -> AgentState:
        """执行智能工具选择"""
        try:
            current_subtask = self._get_current_subtask(state)
            if not current_subtask:
                state["error"] = "没有找到当前子任务"
                return state
            
            # 构建任务需求
            task_requirement = TaskRequirement(
                task_type=current_subtask.get("name", ""),
                input_data_type=state.get("input_data_type", "文本"),
                expected_output_type=current_subtask.get("expected_output", "处理结果"),
                performance_requirements=state.get("performance_requirements", {}),
                constraints=current_subtask.get("constraints", []),
                context=state.get("context", {})
            )
            
            # 智能选择工具
            selected_tools = self.tool_selector.select_tools(task_requirement, max_tools=3)
            
            # 生成工具选择的确认Prompt
            context = PromptContext(
                task_type=current_subtask.get("name", ""),
                user_input=f"推荐工具: {[tool[0] for tool in selected_tools]}",
                current_step="工具选择确认",
                available_tools=[tool[0] for tool in selected_tools],
                data_sources=state.get("data_sources", []),
                constraints=task_requirement.constraints,
                examples=[],
                domain_knowledge=[]
            )
            
            confirmation_prompt = self.prompt_generator.generate_prompt(
                prompt_type="tool_selection",
                context=context,
                target_level=PromptLevel.LEVEL_2
            )
            
            # LLM确认工具选择
            llm_client = self.llm_provider.get_client(is_reason=True)
            confirmation_response = llm_client.chat_completion(
                messages=[{"role": "user", "content": confirmation_prompt}],
                temperature=0.1
            )
            
            # 更新状态
            state["selected_tools"] = selected_tools
            state["tool_selection_reasoning"] = confirmation_response
            state["current_step"] = "工具选择完成"
            
            return state
            
        except Exception as e:
            state["error"] = f"工具选择失败: {str(e)}"
            return state
    
    def _get_current_subtask(self, state: AgentState) -> Dict[str, Any]:
        """获取当前子任务"""
        subtasks = state.get("subtasks", [])
        current_task_index = state.get("current_task_index", 0)
        
        if 0 <= current_task_index < len(subtasks):
            return subtasks[current_task_index]
        return {}


class EnhancedSymbolMatchingNode:
    """增强的符号匹配节点"""
    
    def __init__(self):
        self.symbol_matcher = IntelligentSymbolMatcher()
        self.prompt_generator = DynamicPromptGenerator()
        self.llm_provider = LLMProvider()
    
    def __call__(self, state: AgentState) -> AgentState:
        """执行智能符号匹配"""
        try:
            # 获取需要标绘的数据
            plotting_data = state.get("plotting_data", [])
            if not plotting_data:
                state["error"] = "没有找到需要标绘的数据"
                return state
            
            symbol_matches = []
            
            for data_item in plotting_data:
                # 构建标绘请求
                plotting_request = PlottingRequest(
                    object_type=data_item.get("type", "未知对象"),
                    object_name=data_item.get("name", ""),
                    object_properties=data_item.get("properties", {}),
                    context=state.get("context", {}),
                    style_preferences=state.get("style_preferences", {}),
                    scale_level=state.get("scale_level", "medium"),
                    map_theme=state.get("map_theme", "civilian")
                )
                
                # 智能匹配符号
                matches = self.symbol_matcher.match_symbol(plotting_request)
                
                if matches:
                    best_match = matches[0]  # 选择最佳匹配
                    symbol_matches.append({
                        "data_item": data_item,
                        "symbol_id": best_match[0],
                        "confidence": best_match[1],
                        "rendering_params": best_match[2]
                    })
            
            # 生成符号选择说明Prompt
            context = PromptContext(
                task_type="符号匹配",
                user_input=f"为{len(plotting_data)}个对象匹配了符号",
                current_step="符号匹配完成",
                available_tools=["符号渲染器"],
                data_sources=["符号库"],
                constraints=state.get("cartographic_constraints", []),
                examples=[],
                domain_knowledge=["制图规范", "符号语义"]
            )
            
            explanation_prompt = self.prompt_generator.generate_prompt(
                prompt_type="symbol_matching",
                context=context,
                target_level=PromptLevel.LEVEL_1
            )
            
            # 生成符号选择说明
            llm_client = self.llm_provider.get_client(is_reason=False, speed_first=True)
            explanation = llm_client.chat_completion(
                messages=[{"role": "user", "content": explanation_prompt}],
                temperature=0.3
            )
            
            # 更新状态
            state["symbol_matches"] = symbol_matches
            state["symbol_explanation"] = explanation
            state["current_step"] = "符号匹配完成"
            
            return state
            
        except Exception as e:
            state["error"] = f"符号匹配失败: {str(e)}"
            return state


class EnhancedDataProcessingNode:
    """增强的数据处理节点"""
    
    def __init__(self):
        self.prompt_generator = DynamicPromptGenerator()
        self.tool_selector = IntelligentToolSelector()
        self.llm_provider = LLMProvider()
    
    def __call__(self, state: AgentState) -> AgentState:
        """执行数据处理"""
        try:
            # 获取选择的工具
            selected_tools = state.get("selected_tools", [])
            if not selected_tools:
                state["error"] = "没有选择的工具"
                return state
            
            # 使用第一个推荐工具
            primary_tool = selected_tools[0]
            tool_name = primary_tool[0]
            tool_params = primary_tool[2]
            
            # 构建数据处理Prompt
            context = PromptContext(
                task_type="数据处理",
                user_input=state.get("work_content", ""),
                current_step="数据处理执行",
                available_tools=[tool_name],
                data_sources=state.get("data_sources", []),
                constraints=state.get("processing_constraints", []),
                examples=[],
                domain_knowledge=[]
            )
            
            processing_prompt = self.prompt_generator.generate_prompt(
                prompt_type="data_processing",
                context=context,
                target_level=PromptLevel.LEVEL_2
            )
            
            # 执行数据处理
            processing_result = self._execute_tool(tool_name, tool_params, state)
            
            # 生成处理结果说明
            llm_client = self.llm_provider.get_client(is_reason=False)
            result_summary = llm_client.chat_completion(
                messages=[
                    {"role": "user", "content": processing_prompt},
                    {"role": "assistant", "content": f"处理结果: {processing_result}"}
                ],
                temperature=0.2
            )
            
            # 更新状态
            state["processing_result"] = processing_result
            state["result_summary"] = result_summary
            state["current_step"] = "数据处理完成"
            
            return state
            
        except Exception as e:
            state["error"] = f"数据处理失败: {str(e)}"
            return state
    
    def _execute_tool(self, tool_name: str, tool_params: Dict[str, Any], state: AgentState) -> Any:
        """执行具体工具"""
        # 这里应该根据tool_name调用相应的工具
        # 简化示例，实际应该有工具注册表和调用机制
        
        if tool_name == "PostgreSQLDB":
            # 模拟数据库查询
            return {"status": "success", "data": "查询结果", "count": 10}
        elif tool_name == "VectorSearch":
            # 模拟向量搜索
            return {"status": "success", "matches": ["匹配1", "匹配2"], "scores": [0.9, 0.8]}
        else:
            return {"status": "unknown_tool", "message": f"未知工具: {tool_name}"}


# 使用示例：如何在现有的main_graph.py中集成这些增强节点
def create_enhanced_graph():
    """创建增强的LangGraph"""
    from langgraph.graph import StateGraph
    
    # 创建增强节点实例
    enhanced_decomposition = EnhancedTaskDecompositionNode()
    enhanced_tool_selection = EnhancedToolSelectionNode()
    enhanced_symbol_matching = EnhancedSymbolMatchingNode()
    enhanced_data_processing = EnhancedDataProcessingNode()
    
    # 构建图
    graph = StateGraph(AgentState)
    
    # 添加增强节点
    graph.add_node("enhanced_decomposition", enhanced_decomposition)
    graph.add_node("enhanced_tool_selection", enhanced_tool_selection)
    graph.add_node("enhanced_symbol_matching", enhanced_symbol_matching)
    graph.add_node("enhanced_data_processing", enhanced_data_processing)
    
    # 设置边
    graph.add_edge("enhanced_decomposition", "enhanced_tool_selection")
    graph.add_edge("enhanced_tool_selection", "enhanced_data_processing")
    graph.add_edge("enhanced_data_processing", "enhanced_symbol_matching")
    
    # 设置入口点
    graph.set_entry_point("enhanced_decomposition")
    
    return graph.compile()


if __name__ == "__main__":
    # 测试增强节点
    test_state = AgentState(
        work_content="标绘中国台湾省的港口数据",
        task_type="地图标绘",
        available_tools=["PostgreSQLDB", "VectorSearch"],
        data_sources=["港口数据库"],
        context={"region": "台湾省", "purpose": "商业标绘"}
    )
    
    # 测试任务分解节点
    decomposition_node = EnhancedTaskDecompositionNode()
    result_state = decomposition_node(test_state)
    
    print("任务分解结果:")
    print(f"子任务数量: {len(result_state.get('subtasks', []))}")
    print(f"状态: {result_state.get('task_status')}")
    print(f"当前步骤: {result_state.get('current_step')}")
