# NewsEventLine 语义向量库增强方案

## 概述

本方案基于现有的NewsEventLine项目，通过引入语义向量库技术，实现从静态配置到动态检索的智能化改造。系统能够自适应地生成Prompt、选择工具、匹配符号，大幅提升工作流的智能化水平。

## 核心设计理念

### 1. 从静态到动态
- **传统方式**: 硬编码的配置文件、固定的Prompt模板、预定义的工具选择
- **改造后**: 基于语义检索的动态配置、自适应Prompt生成、智能工具选择

### 2. 分层知识架构
```
元知识层 (Meta Layer)
├── Prompt模板库
├── 工作流模式库
└── 系统能力描述

领域知识层 (Domain Layer)  
├── 制图规则库
├── 符号语义库
└── 数据源规格库

执行知识层 (Execution Layer)
├── 工具调用规则库
├── API规格库
└── 错误处理策略库
```

### 3. 多级Prompt体系
- **0级Prompt**: 元Prompt，用于生成其他Prompt
- **1级Prompt**: 中间Prompt，包含基本结构和规则
- **2级Prompt**: 执行Prompt，直接用于LLM调用

## 系统架构

```mermaid
graph TB
    subgraph "语义向量库层"
        A[Prompt模板库] 
        B[工具调用规则库]
        C[符号库语义描述]
        D[制图规则库]
        E[数据源描述库]
        F[工作流模板库]
    end
    
    subgraph "检索增强层"
        G[语义检索引擎]
        H[上下文构建器]
        I[Prompt动态生成器]
        J[工具选择器]
        K[符号匹配器]
    end
    
    subgraph "执行层"
        L[LangGraph Agent]
        M[工具调用执行器]
        N[标绘渲染器]
    end
    
    A --> G
    B --> G
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> H
    G --> I
    G --> J
    G --> K
    
    H --> L
    I --> L
    J --> M
    K --> N
```

## 核心组件

### 1. 语义知识管理器 (`SemanticKnowledgeManager`)
- **功能**: 管理所有类型的语义知识条目
- **特性**: 
  - 支持多种知识类型和层级
  - 基于FAISS的高效向量检索
  - 灵活的过滤和排序机制
- **使用场景**: 存储和检索Prompt模板、工具规则、符号描述等

### 2. 动态Prompt生成器 (`DynamicPromptGenerator`)
- **功能**: 根据任务上下文动态生成执行Prompt
- **特性**:
  - 三级Prompt生成体系
  - 上下文感知的内容组装
  - 自适应的格式调整
- **使用场景**: 任务分解、工具选择、符号匹配等各个环节

### 3. 智能工具选择器 (`IntelligentToolSelector`)
- **功能**: 基于任务需求智能选择最合适的工具
- **特性**:
  - 多维度匹配算法
  - 性能和约束考虑
  - 参数自动推荐
- **使用场景**: 数据检索、处理、可视化等工具选择

### 4. 智能符号匹配器 (`IntelligentSymbolMatcher`)
- **功能**: 为地图对象智能匹配最合适的符号
- **特性**:
  - 语义相似度匹配
  - 制图规则应用
  - 视觉效果优化
- **使用场景**: 地图标绘、符号选择、样式配置

## 知识库设计

### 知识分类体系

#### 元知识层
1. **Prompt模板**
   - 任务分解模板
   - 工具选择模板
   - 数据分析模板
   - 结果综合模板

2. **工作流模式**
   - 顺序执行流程
   - 并行执行流程
   - 条件分支流程
   - 错误恢复流程

#### 领域知识层
1. **制图规则**
   - 比例尺规则
   - 符号使用规则
   - 颜色搭配规则
   - 布局设计规则

2. **符号语义**
   - 点符号 (港口、机场、城市等)
   - 线符号 (道路、河流、边界等)
   - 面符号 (行政区、水域、用地等)

#### 执行知识层
1. **工具调用规则**
   - 数据库工具 (PostgreSQL查询)
   - 搜索工具 (向量搜索)
   - 可视化工具 (地图渲染)

2. **API规格**
   - REST API规格
   - 内部API调用
   - 错误处理策略

## 实施步骤

### 第一阶段：基础设施搭建
1. **安装依赖**
   ```bash
   pip install faiss-cpu pandas numpy
   ```

2. **初始化知识库**
   ```bash
   python scripts/initialize_semantic_knowledge.py
   ```

3. **验证基础功能**
   ```python
   from tool.semantic_knowledge_manager import SemanticKnowledgeManager
   manager = SemanticKnowledgeManager()
   manager.initialize()
   ```

### 第二阶段：核心组件集成
1. **集成动态Prompt生成**
   ```python
   from tool.dynamic_prompt_generator import DynamicPromptGenerator
   generator = DynamicPromptGenerator()
   ```

2. **集成智能工具选择**
   ```python
   from tool.intelligent_tool_selector import IntelligentToolSelector
   selector = IntelligentToolSelector()
   ```

3. **集成符号匹配**
   ```python
   from tool.intelligent_symbol_matcher import IntelligentSymbolMatcher
   matcher = IntelligentSymbolMatcher()
   ```

### 第三阶段：LangGraph增强
1. **替换现有节点**
   - 使用 `EnhancedTaskDecompositionNode` 替换原有任务分解节点
   - 使用 `EnhancedToolSelectionNode` 增强工具选择
   - 使用 `EnhancedSymbolMatchingNode` 增强符号匹配

2. **更新工作流**
   ```python
   from langgraph_agent.enhanced_nodes import create_enhanced_graph
   enhanced_graph = create_enhanced_graph()
   ```

## 使用示例

### 1. 动态Prompt生成
```python
from tool.dynamic_prompt_generator import DynamicPromptGenerator, PromptContext

generator = DynamicPromptGenerator()
context = PromptContext(
    task_type="地图标绘",
    user_input="标绘台湾省港口数据",
    current_step="任务分解",
    available_tools=["数据库查询", "向量搜索"],
    data_sources=["港口数据库"],
    constraints=["确保数据准确性"],
    examples=[],
    domain_knowledge=["港口分类", "地图符号"]
)

prompt = generator.generate_prompt("task_decomposition", context)
print(prompt)
```

### 2. 智能工具选择
```python
from tool.intelligent_tool_selector import IntelligentToolSelector, TaskRequirement

selector = IntelligentToolSelector()
requirement = TaskRequirement(
    task_type="数据检索",
    input_data_type="地理位置",
    expected_output_type="港口列表",
    performance_requirements={'accuracy': 0.95},
    constraints=["实时处理"],
    context={"region": "台湾省"}
)

selected_tools = selector.select_tools(requirement)
for tool_name, score, params in selected_tools:
    print(f"推荐工具: {tool_name}, 置信度: {score:.3f}")
```

### 3. 智能符号匹配
```python
from tool.intelligent_symbol_matcher import IntelligentSymbolMatcher, PlottingRequest

matcher = IntelligentSymbolMatcher()
request = PlottingRequest(
    object_type="港口",
    object_name="高雄港",
    object_properties={"type": "商港", "scale": "大型"},
    context={"region": "台湾省"},
    style_preferences={"color": "blue"},
    scale_level="medium",
    map_theme="civilian"
)

matches = matcher.match_symbol(request)
for symbol_id, score, params in matches:
    print(f"推荐符号: {symbol_id}, 匹配度: {score:.3f}")
```

## 配置管理

### 知识库配置
- 配置文件: `config/semantic_knowledge_config.py`
- 支持自定义知识分类和质量标准
- 提供模板验证和管理功能

### 性能优化
- 向量索引优化: 使用FAISS的高效索引结构
- 缓存机制: 缓存常用的检索结果
- 批量处理: 支持批量知识条目操作

## 扩展指南

### 添加新的知识类型
1. 在 `KnowledgeType` 枚举中添加新类型
2. 在配置文件中定义内容结构
3. 实现相应的检索和匹配逻辑

### 自定义Prompt模板
1. 创建新的模板条目
2. 定义变量和输出格式
3. 添加使用示例和质量标准

### 集成新工具
1. 在工具规则库中添加工具描述
2. 实现工具调用接口
3. 配置性能指标和约束条件

## 监控和维护

### 性能监控
- 检索延迟监控
- 匹配准确率统计
- 资源使用情况跟踪

### 质量保证
- 定期评估知识库质量
- 更新过时的规则和模板
- 收集用户反馈优化系统

## 总结

通过引入语义向量库技术，NewsEventLine项目实现了从静态配置到动态智能的重大升级：

1. **智能化程度大幅提升**: 系统能够根据上下文自适应地生成Prompt、选择工具、匹配符号
2. **可扩展性显著增强**: 新的知识可以轻松添加到向量库中，无需修改代码
3. **维护成本大幅降低**: 规则和配置的更新变成了知识库的增量更新
4. **用户体验明显改善**: 更准确的结果、更智能的推荐、更灵活的配置

这套方案为地图标绘和数据可视化领域的AI应用提供了一个可参考的智能化改造范例。
