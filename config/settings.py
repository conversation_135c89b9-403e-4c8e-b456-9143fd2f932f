import os
from dotenv import load_dotenv

# 加载 .env 文件
load_dotenv()

class ClientConfig:
    client_name = os.getenv("CLIENT")
    model_type = os.getenv("MODEL_TYPE")

class AzureConfig:
    DEPLOYMENT_NAME = os.getenv("AZURE_DEPLOYMENT_NAME")
    ENDPOINT = os.getenv("AZURE_ENDPOINT")
    API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
    API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")

    ENDPOINT_DEEPSEEK = os.getenv("AZURE_ENDPOINT_DEEPSEEK")
    MODEL_NAME_DEEPSEEK = os.getenv("AZURE_MODEL_NAME_DEEPSEEK")
    API_VERSION_DEEPSEEK = os.getenv("AZURE_API_VERSION_DEEPSEEK")

    EMBEDDING_DEPLOYMENT_NAME = os.getenv("AZURE_EMBEDDING_DEPLOYMENT_NAME")
    EMBEDDING_API_VERSION = os.getenv("AZURE_EMBEDDING_API_VERSION")
    EMBEDDING_ENDPOINT = os.getenv("AZURE_EMBEDDING_ENDPOINT")

class DeepseekConfig:
    DEPLOYMENT = os.getenv("DEEPSEEK_DEPLOYMENT_NAME")
    API_KEY = os.getenv("DEEPSEEK_OPENAI_API_KEY")
    DEPLOYMENT_SILICON = os.getenv("DEEPSEEK_DEPLOYMENT_NAME_SILICON")
    API_KEY_SILICON = os.getenv("DEEPSEEK_OPENAI_API_KEY_SILICON")
    DEPLOYMENT_ALIYUN = os.getenv("DEEPSEEK_DEPLOYMENT_NAME_ALIYUN")
    API_KEY_ALIYUN = os.getenv("DEEPSEEK_OPENAI_API_KEY_ALIYUN")

class AliyunConfig:
    API_KEY = os.getenv("ALIYUN_API_KEY")
    FAST_MODEL_NAME = os.getenv("ALIYUN_FAST_MODEL_NAME")
    COMPLEX_MODEL_NAME = os.getenv("ALIYUN_COMPLEX_MODEL_NAME")
    API_BASE = os.getenv("ALIYUN_API_BASE")
