"""
语义知识库配置文件
定义各类知识的分层分类结构和内容设计规范
"""

from typing import Dict, List, Any
from enum import Enum


class KnowledgeHierarchy:
    """知识层次结构定义"""
    
    # 元知识层 - 用于生成其他知识的知识
    META_LAYER = {
        "prompt_templates": {
            "description": "用于生成各种执行Prompt的模板",
            "subcategories": {
                "task_decomposition": "任务分解Prompt模板",
                "tool_selection": "工具选择Prompt模板", 
                "data_analysis": "数据分析Prompt模板",
                "result_synthesis": "结果综合Prompt模板",
                "error_handling": "错误处理Prompt模板"
            },
            "content_structure": {
                "template": "Prompt模板字符串，包含变量占位符",
                "variables": "模板变量列表",
                "output_format": "期望的输出格式",
                "usage_context": "使用场景和上下文",
                "examples": "使用示例"
            }
        },
        "workflow_patterns": {
            "description": "标准工作流模式和决策规则",
            "subcategories": {
                "sequential_flow": "顺序执行流程",
                "parallel_flow": "并行执行流程",
                "conditional_flow": "条件分支流程",
                "iterative_flow": "迭代循环流程",
                "error_recovery": "错误恢复流程"
            },
            "content_structure": {
                "workflow_steps": "工作流步骤定义",
                "decision_points": "决策点和条件",
                "error_handling": "错误处理策略",
                "performance_metrics": "性能指标",
                "optimization_rules": "优化规则"
            }
        },
        "system_capabilities": {
            "description": "系统能力描述和边界定义",
            "subcategories": {
                "data_processing": "数据处理能力",
                "visualization": "可视化能力",
                "analysis": "分析能力",
                "integration": "集成能力"
            },
            "content_structure": {
                "capability_description": "能力详细描述",
                "input_constraints": "输入约束条件",
                "output_specifications": "输出规格说明",
                "performance_limits": "性能限制",
                "usage_guidelines": "使用指导"
            }
        }
    }
    
    # 领域知识层 - 特定领域的专业知识
    DOMAIN_LAYER = {
        "cartographic_rules": {
            "description": "地图制作和标绘的专业规则",
            "subcategories": {
                "scale_rules": "比例尺相关规则",
                "symbol_rules": "符号使用规则",
                "color_rules": "颜色搭配规则",
                "layout_rules": "布局设计规则",
                "annotation_rules": "注记标注规则"
            },
            "content_structure": {
                "rule_definition": "规则定义和说明",
                "application_conditions": "适用条件",
                "constraint_parameters": "约束参数",
                "exception_cases": "例外情况",
                "reference_standards": "参考标准"
            }
        },
        "symbol_semantics": {
            "description": "地图符号的语义描述和使用规范",
            "subcategories": {
                "point_symbols": "点符号",
                "line_symbols": "线符号",
                "area_symbols": "面符号",
                "text_symbols": "文字符号",
                "composite_symbols": "复合符号"
            },
            "content_structure": {
                "symbol_metadata": "符号元数据",
                "semantic_tags": "语义标签",
                "visual_properties": "视觉属性",
                "usage_context": "使用上下文",
                "cartographic_rules": "制图规则"
            }
        },
        "data_source_specs": {
            "description": "数据源规格和处理规则",
            "subcategories": {
                "database_schemas": "数据库模式",
                "api_specifications": "API规格",
                "file_formats": "文件格式",
                "data_quality": "数据质量标准",
                "transformation_rules": "数据转换规则"
            },
            "content_structure": {
                "source_description": "数据源描述",
                "schema_definition": "模式定义",
                "access_methods": "访问方法",
                "quality_metrics": "质量指标",
                "processing_guidelines": "处理指导"
            }
        }
    }
    
    # 执行知识层 - 具体执行相关的知识
    EXECUTION_LAYER = {
        "tool_invocation": {
            "description": "工具调用规则和参数配置",
            "subcategories": {
                "database_tools": "数据库工具",
                "search_tools": "搜索工具",
                "visualization_tools": "可视化工具",
                "analysis_tools": "分析工具",
                "communication_tools": "通信工具"
            },
            "content_structure": {
                "tool_metadata": "工具元数据",
                "parameter_specifications": "参数规格",
                "usage_examples": "使用示例",
                "performance_metrics": "性能指标",
                "error_handling": "错误处理"
            }
        },
        "api_specifications": {
            "description": "API规格和调用方法",
            "subcategories": {
                "rest_apis": "REST API",
                "graphql_apis": "GraphQL API",
                "websocket_apis": "WebSocket API",
                "internal_apis": "内部API"
            },
            "content_structure": {
                "endpoint_definition": "端点定义",
                "request_format": "请求格式",
                "response_format": "响应格式",
                "authentication": "认证方式",
                "rate_limits": "速率限制"
            }
        },
        "error_handling": {
            "description": "错误处理和恢复策略",
            "subcategories": {
                "data_errors": "数据错误",
                "network_errors": "网络错误",
                "system_errors": "系统错误",
                "user_errors": "用户错误",
                "integration_errors": "集成错误"
            },
            "content_structure": {
                "error_classification": "错误分类",
                "detection_methods": "检测方法",
                "recovery_strategies": "恢复策略",
                "fallback_options": "备选方案",
                "logging_requirements": "日志要求"
            }
        }
    }


class PromptTemplateCategories:
    """Prompt模板分类和设计规范"""
    
    # 0级Prompt - 元Prompt，用于生成其他Prompt
    LEVEL_0_PROMPTS = {
        "meta_prompt_generator": {
            "purpose": "生成特定任务的Prompt模板",
            "structure": {
                "role_definition": "定义AI助手的角色和专业背景",
                "task_objective": "明确任务目标和期望结果",
                "input_specification": "输入数据的格式和要求",
                "output_specification": "输出结果的格式和标准",
                "constraint_definition": "约束条件和限制",
                "example_provision": "提供示例和参考"
            },
            "variables": [
                "task_domain",      # 任务领域
                "complexity_level", # 复杂度级别
                "output_format",    # 输出格式
                "quality_requirements" # 质量要求
            ]
        }
    }
    
    # 1级Prompt - 中间Prompt，包含基本结构和规则
    LEVEL_1_PROMPTS = {
        "task_decomposition": {
            "purpose": "将复杂任务分解为可执行的子任务",
            "key_elements": [
                "任务理解和分析",
                "分解原则和方法",
                "依赖关系识别",
                "优先级排序",
                "可行性评估"
            ],
            "output_format": "结构化JSON",
            "quality_criteria": [
                "完整性 - 覆盖所有必要步骤",
                "原子性 - 每个子任务可独立执行",
                "可行性 - 具备执行条件",
                "逻辑性 - 步骤顺序合理"
            ]
        },
        "tool_selection": {
            "purpose": "根据任务需求选择最合适的工具",
            "key_elements": [
                "需求分析",
                "工具能力评估",
                "匹配度计算",
                "性能考虑",
                "风险评估"
            ],
            "output_format": "工具推荐列表",
            "quality_criteria": [
                "准确性 - 工具功能匹配需求",
                "效率性 - 性能满足要求",
                "可靠性 - 稳定性和错误处理",
                "兼容性 - 与系统集成度"
            ]
        },
        "symbol_matching": {
            "purpose": "为地图对象选择合适的符号",
            "key_elements": [
                "对象类型识别",
                "语义匹配",
                "制图规则应用",
                "视觉效果考虑",
                "上下文适配"
            ],
            "output_format": "符号配置",
            "quality_criteria": [
                "语义准确性 - 符号含义匹配对象",
                "视觉清晰性 - 符号易于识别",
                "规范符合性 - 遵循制图标准",
                "美观协调性 - 整体视觉效果"
            ]
        }
    }
    
    # 2级Prompt - 执行Prompt，直接用于LLM调用
    LEVEL_2_PROMPTS = {
        "execution_templates": {
            "structure": {
                "system_message": "系统角色和能力定义",
                "context_information": "任务上下文和背景信息",
                "specific_instructions": "具体执行指令",
                "format_requirements": "输出格式要求",
                "quality_standards": "质量标准和检查点",
                "example_demonstrations": "示例演示"
            },
            "optimization_principles": [
                "清晰性 - 指令明确无歧义",
                "完整性 - 包含所有必要信息",
                "简洁性 - 避免冗余和复杂",
                "可操作性 - 便于LLM理解执行"
            ]
        }
    }


class ToolRuleCategories:
    """工具调用规则分类"""
    
    DATA_RETRIEVAL_TOOLS = {
        "database_query": {
            "description": "数据库查询工具",
            "parameters": {
                "sql_query": "SQL查询语句",
                "parameters": "查询参数",
                "limit": "结果数量限制",
                "timeout": "查询超时时间"
            },
            "constraints": [
                "SQL注入防护",
                "查询复杂度限制",
                "结果大小限制",
                "并发访问控制"
            ],
            "performance_metrics": {
                "response_time": "响应时间",
                "accuracy": "查询准确率",
                "throughput": "吞吐量"
            }
        },
        "vector_search": {
            "description": "向量语义搜索工具",
            "parameters": {
                "query_text": "查询文本",
                "top_k": "返回结果数量",
                "similarity_threshold": "相似度阈值",
                "filters": "过滤条件"
            },
            "constraints": [
                "向量维度匹配",
                "索引大小限制",
                "查询频率限制"
            ],
            "performance_metrics": {
                "search_latency": "搜索延迟",
                "recall_rate": "召回率",
                "precision_rate": "精确率"
            }
        }
    }
    
    VISUALIZATION_TOOLS = {
        "map_renderer": {
            "description": "地图渲染工具",
            "parameters": {
                "data_layers": "数据图层",
                "symbol_config": "符号配置",
                "style_settings": "样式设置",
                "output_format": "输出格式"
            },
            "constraints": [
                "数据量限制",
                "渲染复杂度限制",
                "输出尺寸限制"
            ],
            "performance_metrics": {
                "render_time": "渲染时间",
                "image_quality": "图像质量",
                "memory_usage": "内存使用"
            }
        }
    }


class SymbolSemanticCategories:
    """符号语义分类"""
    
    POINT_SYMBOLS = {
        "infrastructure": {
            "ports": "港口符号",
            "airports": "机场符号", 
            "stations": "车站符号",
            "facilities": "设施符号"
        },
        "natural_features": {
            "peaks": "山峰符号",
            "springs": "泉水符号",
            "caves": "洞穴符号"
        },
        "administrative": {
            "capitals": "首府符号",
            "cities": "城市符号",
            "boundaries": "边界点符号"
        }
    }
    
    LINE_SYMBOLS = {
        "transportation": {
            "roads": "道路符号",
            "railways": "铁路符号",
            "waterways": "水路符号"
        },
        "boundaries": {
            "international": "国界符号",
            "administrative": "行政界符号",
            "natural": "自然界符号"
        },
        "utilities": {
            "power_lines": "电力线符号",
            "pipelines": "管道符号"
        }
    }
    
    AREA_SYMBOLS = {
        "land_use": {
            "urban": "城市用地",
            "agricultural": "农业用地",
            "forest": "森林用地",
            "water": "水域"
        },
        "administrative": {
            "countries": "国家区域",
            "provinces": "省份区域",
            "cities": "城市区域"
        }
    }


# 配置验证和管理
class KnowledgeConfigManager:
    """知识配置管理器"""
    
    @staticmethod
    def validate_knowledge_structure(knowledge_data: Dict[str, Any]) -> bool:
        """验证知识结构的完整性"""
        required_fields = ["id", "knowledge_type", "layer", "title", "description", "content"]
        return all(field in knowledge_data for field in required_fields)
    
    @staticmethod
    def get_template_by_category(category: str, subcategory: str = None) -> Dict[str, Any]:
        """根据分类获取模板"""
        # 实现模板检索逻辑
        pass
    
    @staticmethod
    def get_quality_criteria(knowledge_type: str) -> List[str]:
        """获取质量标准"""
        quality_criteria = {
            "prompt_template": [
                "清晰性 - 指令明确无歧义",
                "完整性 - 包含所有必要信息", 
                "可操作性 - 便于执行",
                "一致性 - 格式和风格统一"
            ],
            "tool_rule": [
                "准确性 - 参数和约束正确",
                "完整性 - 覆盖所有使用场景",
                "可靠性 - 错误处理完善",
                "性能性 - 满足性能要求"
            ],
            "symbol_semantic": [
                "语义准确性 - 符号含义正确",
                "视觉清晰性 - 符号易于识别",
                "标准符合性 - 遵循制图规范",
                "上下文适配性 - 适应不同场景"
            ]
        }
        return quality_criteria.get(knowledge_type, [])


if __name__ == "__main__":
    # 配置使用示例
    config_manager = KnowledgeConfigManager()
    
    # 获取Prompt模板质量标准
    prompt_criteria = config_manager.get_quality_criteria("prompt_template")
    print("Prompt模板质量标准:")
    for criterion in prompt_criteria:
        print(f"- {criterion}")
    
    # 验证知识结构
    sample_knowledge = {
        "id": "test_001",
        "knowledge_type": "prompt_template",
        "layer": "meta_layer",
        "title": "测试模板",
        "description": "测试用的Prompt模板",
        "content": {"template": "测试内容"}
    }
    
    is_valid = config_manager.validate_knowledge_structure(sample_knowledge)
    print(f"\n知识结构验证结果: {is_valid}")
