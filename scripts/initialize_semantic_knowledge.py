#!/usr/bin/env python3
"""
语义知识库初始化脚本
用于预置基础的知识条目，包括Prompt模板、工具规则、符号语义等
"""

import sys
import os
import uuid
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tool.semantic_knowledge_manager import (
    SemanticKnowledgeManager, 
    SemanticKnowledge, 
    KnowledgeType, 
    KnowledgeLayer
)


def initialize_prompt_templates(knowledge_manager: SemanticKnowledgeManager):
    """初始化Prompt模板"""
    
    # 任务分解Prompt模板
    task_decomposition_template = SemanticKnowledge(
        id=str(uuid.uuid4()),
        knowledge_type=KnowledgeType.PROMPT_TEMPLATE,
        layer=KnowledgeLayer.META,
        title="任务分解Prompt模板",
        description="用于将复杂任务分解为可执行子任务的Prompt模板",
        content={
            "template": """
你是一位专业的任务分解专家，擅长将复杂任务分解为清晰的执行步骤。

## 任务信息
- 任务类型：{task_type}
- 用户需求：{user_input}
- 可用资源：{available_resources}

## 分解原则
1. 每个子任务应该是原子性的，可独立执行
2. 子任务之间的依赖关系要明确
3. 考虑实际执行的可行性和效率
4. 确保分解后的任务覆盖原始需求

## 输出格式
请按以下JSON格式输出：
{{
  "subtasks": [
    {{
      "id": "task_1",
      "name": "子任务名称",
      "description": "详细描述",
      "dependencies": ["依赖的任务ID"],
      "estimated_time": "预估时间",
      "required_tools": ["需要的工具"]
    }}
  ]
}}
""",
            "variables": ["task_type", "user_input", "available_resources"],
            "output_format": "json",
            "usage_context": ["任务规划", "工作流设计"]
        },
        tags=["任务分解", "工作流", "规划"],
        usage_context=["LangGraph节点", "任务规划阶段"],
        priority=5
    )
    
    # 工具选择Prompt模板
    tool_selection_template = SemanticKnowledge(
        id=str(uuid.uuid4()),
        knowledge_type=KnowledgeType.PROMPT_TEMPLATE,
        layer=KnowledgeLayer.META,
        title="工具选择Prompt模板",
        description="用于根据任务需求选择最合适工具的Prompt模板",
        content={
            "template": """
你是一位工具选择专家，需要为给定任务选择最合适的工具。

## 任务需求
- 任务描述：{task_description}
- 输入数据类型：{input_type}
- 期望输出：{expected_output}
- 性能要求：{performance_requirements}

## 可用工具
{available_tools}

## 选择标准
1. 功能匹配度：工具能力与任务需求的匹配程度
2. 性能指标：处理速度、准确率、资源消耗
3. 兼容性：与现有系统的集成难度
4. 可靠性：工具的稳定性和错误处理能力

## 输出要求
请选择最合适的工具并说明理由，格式如下：
{{
  "selected_tool": "工具名称",
  "confidence": 0.95,
  "reasoning": "选择理由",
  "parameters": {{"参数名": "参数值"}},
  "alternatives": ["备选工具1", "备选工具2"]
}}
""",
            "variables": ["task_description", "input_type", "expected_output", "performance_requirements", "available_tools"],
            "output_format": "json"
        },
        tags=["工具选择", "决策", "优化"],
        usage_context=["工具调用节点", "资源分配"],
        priority=5
    )
    
    knowledge_manager.add_knowledge(task_decomposition_template)
    knowledge_manager.add_knowledge(tool_selection_template)
    print("✓ Prompt模板初始化完成")


def initialize_tool_rules(knowledge_manager: SemanticKnowledgeManager):
    """初始化工具调用规则"""
    
    # 数据库查询工具规则
    db_query_rule = SemanticKnowledge(
        id=str(uuid.uuid4()),
        knowledge_type=KnowledgeType.TOOL_RULE,
        layer=KnowledgeLayer.EXECUTION,
        title="数据库查询工具规则",
        description="PostgreSQL数据库查询工具的调用规则和最佳实践",
        content={
            "tool_name": "PostgreSQLDB",
            "category": "data_retrieval",
            "description": "用于查询PostgreSQL数据库的工具",
            "input_types": ["SQL查询", "表名", "条件参数"],
            "output_types": ["查询结果", "数据记录", "统计信息"],
            "parameters": {
                "sql": {"type": "string", "required": True, "description": "SQL查询语句"},
                "params": {"type": "dict", "required": False, "description": "查询参数"},
                "limit": {"type": "int", "default": 1000, "description": "结果数量限制"}
            },
            "constraints": ["SQL注入防护", "查询超时限制", "结果大小限制"],
            "performance_metrics": {
                "latency": 0.5,
                "accuracy": 0.99,
                "memory_usage": 100
            },
            "usage_examples": [
                {
                    "input": "查询港口数据",
                    "sql": "SELECT * FROM ports WHERE region = %s",
                    "params": {"region": "台湾省"},
                    "output": "港口记录列表"
                }
            ],
            "usage_guidance": "适用于结构化数据查询，支持复杂SQL语句，注意查询性能优化"
        },
        tags=["数据库", "查询", "PostgreSQL"],
        usage_context=["数据检索", "信息查找"],
        priority=4
    )
    
    # 向量搜索工具规则
    vector_search_rule = SemanticKnowledge(
        id=str(uuid.uuid4()),
        knowledge_type=KnowledgeType.TOOL_RULE,
        layer=KnowledgeLayer.EXECUTION,
        title="向量搜索工具规则",
        description="FAISS向量搜索工具的调用规则和配置方法",
        content={
            "tool_name": "VectorSearch",
            "category": "data_retrieval",
            "description": "基于语义相似度的向量搜索工具",
            "input_types": ["文本查询", "向量查询", "过滤条件"],
            "output_types": ["相似文档", "相似度分数", "元数据"],
            "parameters": {
                "query": {"type": "string", "required": True, "description": "搜索查询"},
                "top_k": {"type": "int", "default": 5, "description": "返回结果数量"},
                "filters": {"type": "dict", "required": False, "description": "过滤条件"},
                "threshold": {"type": "float", "default": 0.7, "description": "相似度阈值"}
            },
            "constraints": ["向量维度匹配", "索引大小限制"],
            "performance_metrics": {
                "latency": 0.1,
                "accuracy": 0.85,
                "memory_usage": 200
            },
            "usage_guidance": "适用于语义搜索和相似性匹配，查询质量影响搜索效果"
        },
        tags=["向量搜索", "语义搜索", "FAISS"],
        usage_context=["知识检索", "相似性匹配"],
        priority=4
    )
    
    knowledge_manager.add_knowledge(db_query_rule)
    knowledge_manager.add_knowledge(vector_search_rule)
    print("✓ 工具规则初始化完成")


def initialize_symbol_semantics(knowledge_manager: SemanticKnowledgeManager):
    """初始化符号语义描述"""
    
    # 港口符号
    port_symbol = SemanticKnowledge(
        id=str(uuid.uuid4()),
        knowledge_type=KnowledgeType.SYMBOL_SEMANTIC,
        layer=KnowledgeLayer.DOMAIN,
        title="港口符号",
        description="用于标绘各类港口的地图符号",
        content={
            "symbol_id": "port_001",
            "name": "标准港口符号",
            "category": "point",
            "style": "civilian",
            "description": "用于标绘商业港口、渔港等民用港口设施",
            "semantic_tags": ["港口", "码头", "船舶", "运输", "商业", "民用"],
            "usage_context": ["海运地图", "交通运输图", "经济地图"],
            "visual_properties": {
                "shape": "anchor",
                "color": "#0066CC",
                "size": 12,
                "stroke_width": 2
            },
            "cartographic_rules": {
                "min_scale": "1:50000",
                "max_scale": "1:1000000",
                "label_position": "right"
            },
            "priority": 3
        },
        tags=["港口", "符号", "标绘", "交通"],
        usage_context=["地图制作", "港口标绘"],
        priority=3
    )
    
    # 机场符号
    airport_symbol = SemanticKnowledge(
        id=str(uuid.uuid4()),
        knowledge_type=KnowledgeType.SYMBOL_SEMANTIC,
        layer=KnowledgeLayer.DOMAIN,
        title="机场符号",
        description="用于标绘各类机场的地图符号",
        content={
            "symbol_id": "airport_001",
            "name": "标准机场符号",
            "category": "point",
            "style": "civilian",
            "description": "用于标绘民用机场、军用机场等航空设施",
            "semantic_tags": ["机场", "航空", "飞机", "运输", "民用", "军用"],
            "usage_context": ["航空地图", "交通运输图", "军事地图"],
            "visual_properties": {
                "shape": "airplane",
                "color": "#FF6600",
                "size": 14,
                "stroke_width": 2
            },
            "cartographic_rules": {
                "min_scale": "1:25000",
                "max_scale": "1:2000000",
                "label_position": "bottom"
            },
            "priority": 4
        },
        tags=["机场", "符号", "标绘", "航空"],
        usage_context=["地图制作", "机场标绘"],
        priority=4
    )
    
    knowledge_manager.add_knowledge(port_symbol)
    knowledge_manager.add_knowledge(airport_symbol)
    print("✓ 符号语义初始化完成")


def initialize_cartographic_rules(knowledge_manager: SemanticKnowledgeManager):
    """初始化制图规则"""
    
    # 比例尺规则
    scale_rule = SemanticKnowledge(
        id=str(uuid.uuid4()),
        knowledge_type=KnowledgeType.CARTOGRAPHIC_RULE,
        layer=KnowledgeLayer.DOMAIN,
        title="比例尺符号选择规则",
        description="根据地图比例尺选择合适符号的规则",
        content={
            "scale_rules": {
                "large": {  # 大比例尺 1:10000以下
                    "allowed_categories": ["point", "line", "area", "text"],
                    "symbol_size_factor": 1.5,
                    "detail_level": "high"
                },
                "medium": {  # 中比例尺 1:10000-1:100000
                    "allowed_categories": ["point", "line", "area"],
                    "symbol_size_factor": 1.0,
                    "detail_level": "medium"
                },
                "small": {  # 小比例尺 1:100000以上
                    "allowed_categories": ["point", "area"],
                    "symbol_size_factor": 0.7,
                    "detail_level": "low"
                }
            }
        },
        tags=["比例尺", "符号选择", "制图规范"],
        usage_context=["符号匹配", "地图设计"],
        priority=5
    )
    
    # 主题规则
    theme_rule = SemanticKnowledge(
        id=str(uuid.uuid4()),
        knowledge_type=KnowledgeType.CARTOGRAPHIC_RULE,
        layer=KnowledgeLayer.DOMAIN,
        title="地图主题符号规则",
        description="根据地图主题选择符号样式的规则",
        content={
            "theme_rules": {
                "military": {
                    "preferred_styles": ["military"],
                    "color_scheme": "tactical",
                    "symbol_priority": ["strategic", "tactical", "operational"]
                },
                "civilian": {
                    "preferred_styles": ["civilian", "infrastructure"],
                    "color_scheme": "standard",
                    "symbol_priority": ["public", "commercial", "residential"]
                },
                "transportation": {
                    "preferred_styles": ["infrastructure"],
                    "color_scheme": "transport",
                    "symbol_priority": ["major", "minor", "local"]
                }
            }
        },
        tags=["主题", "样式", "制图规范"],
        usage_context=["符号匹配", "主题地图"],
        priority=4
    )
    
    knowledge_manager.add_knowledge(scale_rule)
    knowledge_manager.add_knowledge(theme_rule)
    print("✓ 制图规则初始化完成")


def initialize_workflow_patterns(knowledge_manager: SemanticKnowledgeManager):
    """初始化工作流模式"""
    
    # 地图标绘工作流
    mapping_workflow = SemanticKnowledge(
        id=str(uuid.uuid4()),
        knowledge_type=KnowledgeType.WORKFLOW_PATTERN,
        layer=KnowledgeLayer.META,
        title="地图标绘工作流模式",
        description="标准的地图标绘任务处理流程",
        content={
            "workflow_steps": [
                {
                    "step": "需求分析",
                    "description": "分析用户标绘需求，确定数据源和输出格式",
                    "inputs": ["用户输入", "任务上下文"],
                    "outputs": ["需求规格", "数据需求"],
                    "tools": ["文本分析", "需求解析"]
                },
                {
                    "step": "数据检索",
                    "description": "从数据库或向量库检索相关数据",
                    "inputs": ["数据需求", "查询条件"],
                    "outputs": ["原始数据", "元数据"],
                    "tools": ["数据库查询", "向量搜索"]
                },
                {
                    "step": "符号匹配",
                    "description": "为数据对象选择合适的地图符号",
                    "inputs": ["数据对象", "制图规则"],
                    "outputs": ["符号配置", "渲染参数"],
                    "tools": ["符号匹配器", "规则引擎"]
                },
                {
                    "step": "地图渲染",
                    "description": "生成最终的地图可视化结果",
                    "inputs": ["符号配置", "地图底图"],
                    "outputs": ["地图图像", "图例"],
                    "tools": ["地图渲染器", "图例生成器"]
                }
            ],
            "decision_points": [
                {
                    "condition": "数据量过大",
                    "action": "启用分页处理"
                },
                {
                    "condition": "符号冲突",
                    "action": "应用冲突解决规则"
                }
            ]
        },
        tags=["工作流", "地图标绘", "流程"],
        usage_context=["任务规划", "流程设计"],
        priority=5
    )
    
    knowledge_manager.add_knowledge(mapping_workflow)
    print("✓ 工作流模式初始化完成")


def main():
    """主函数"""
    print("开始初始化语义知识库...")
    
    # 创建知识库管理器
    knowledge_manager = SemanticKnowledgeManager()
    knowledge_manager.initialize()
    
    # 初始化各类知识
    initialize_prompt_templates(knowledge_manager)
    initialize_tool_rules(knowledge_manager)
    initialize_symbol_semantics(knowledge_manager)
    initialize_cartographic_rules(knowledge_manager)
    initialize_workflow_patterns(knowledge_manager)
    
    print("\n🎉 语义知识库初始化完成！")
    print(f"知识库位置: {knowledge_manager.knowledge_dir}")
    print("现在可以使用动态Prompt生成、智能工具选择和符号匹配功能了。")


if __name__ == "__main__":
    main()
