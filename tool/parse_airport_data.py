import json

from dbfread import DBF
import pandas as pd

from tool.llm_tool import llm_not_reason, get_not_reason_llm_response_with_retry


def convert_dbf_file_2_csv():
    # 读取DBF文件（中文编码尝试GBK/GB2312）
    table = DBF(r'D:\文档\ai制图员\全球机场数据【shp】\全球机场数据【shp】\全球机场.dbf', encoding='utf-8')
    print("DBF文件总记录数:", len(table))  # 检查实际记录数
    # 转换为DataFrame并保存CSV
    df = pd.DataFrame(iter(table))
    df.to_csv('airport_output.csv', index=False, encoding='utf_8_sig')  # 带BOM的UTF-8避免Excel乱码


def  get_airport_info(json_data):
    name = json_data['name']
    abbrev = json_data['abbrev']
    wikipedia = json_data['wikipedia']


def add_detail_info():
    df = pd.read_csv('airport_output.csv', encoding='utf_8_sig')
    prompt = f'''
请根据以下机场信息，从维基百科等官方信息来源，获取该机场的详细信息。
输出要求：
直接输出机场的详细信息，不需要解释说明。

给出的机场信息:

'''
    #遍历df
    for index, row in df.iterrows():
        #将每行数据内容转换成json格式
        print(row)
        json_data = row.to_dict()
        #将json数据写入prompt
        prompt += json.dumps(json_data)
        result = get_not_reason_llm_response_with_retry(prompt)
        print(result)
        df.at[index, '详细信息'] = result

    df.to_csv('airport_output_new.csv', index=False, encoding='utf_8_sig')  # 带BOM的UTF-8避免Excel乱码

if __name__ == '__main__':
    add_detail_info()


