from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import math
from tool.semantic_knowledge_manager import SemanticKnowledgeManager, KnowledgeType, KnowledgeLayer


class SymbolCategory(Enum):
    """符号类别"""
    POINT = "point"              # 点符号
    LINE = "line"                # 线符号  
    AREA = "area"                # 面符号
    TEXT = "text"                # 文字符号
    COMPOSITE = "composite"      # 复合符号


class SymbolStyle(Enum):
    """符号样式"""
    MILITARY = "military"        # 军用符号
    CIVILIAN = "civilian"        # 民用符号
    INFRASTRUCTURE = "infrastructure"  # 基础设施符号
    NATURAL = "natural"          # 自然地理符号
    ADMINISTRATIVE = "administrative"  # 行政符号


@dataclass
class SymbolMetadata:
    """符号元数据"""
    symbol_id: str
    name: str
    category: SymbolCategory
    style: SymbolStyle
    description: str
    semantic_tags: List[str]
    usage_context: List[str]
    visual_properties: Dict[str, Any]
    cartographic_rules: Dict[str, Any]
    priority: int = 1


@dataclass
class PlottingRequest:
    """标绘请求"""
    object_type: str
    object_name: str
    object_properties: Dict[str, Any]
    context: Dict[str, Any]
    style_preferences: Dict[str, Any]
    scale_level: str
    map_theme: str


class IntelligentSymbolMatcher:
    """智能符号匹配器"""
    
    def __init__(self):
        self.knowledge_manager = SemanticKnowledgeManager()
        self.knowledge_manager.initialize()
        self.symbol_registry = {}
        self.cartographic_rules = {}
        self._load_symbol_registry()
        self._load_cartographic_rules()
    
    def _load_symbol_registry(self):
        """加载符号注册表"""
        symbol_results = self.knowledge_manager.search_knowledge(
            query="符号库 地图符号 标绘符号",
            knowledge_types=[KnowledgeType.SYMBOL_SEMANTIC],
            top_k=100
        )
        
        for knowledge, score in symbol_results:
            symbol_info = knowledge.content
            if 'symbol_id' in symbol_info:
                self.symbol_registry[symbol_info['symbol_id']] = SymbolMetadata(
                    symbol_id=symbol_info['symbol_id'],
                    name=symbol_info.get('name', ''),
                    category=SymbolCategory(symbol_info.get('category', 'point')),
                    style=SymbolStyle(symbol_info.get('style', 'civilian')),
                    description=symbol_info.get('description', ''),
                    semantic_tags=symbol_info.get('semantic_tags', []),
                    usage_context=symbol_info.get('usage_context', []),
                    visual_properties=symbol_info.get('visual_properties', {}),
                    cartographic_rules=symbol_info.get('cartographic_rules', {}),
                    priority=symbol_info.get('priority', 1)
                )
    
    def _load_cartographic_rules(self):
        """加载制图规则"""
        rule_results = self.knowledge_manager.search_knowledge(
            query="制图规则 符号选择规则 地图标准",
            knowledge_types=[KnowledgeType.CARTOGRAPHIC_RULE],
            top_k=50
        )
        
        for knowledge, score in rule_results:
            rule_info = knowledge.content
            rule_id = knowledge.id
            self.cartographic_rules[rule_id] = {
                'title': knowledge.title,
                'description': knowledge.description,
                'rules': rule_info,
                'priority': knowledge.priority
            }
    
    def match_symbol(self, plotting_request: PlottingRequest) -> List[Tuple[str, float, Dict[str, Any]]]:
        """
        匹配最合适的符号
        
        Args:
            plotting_request: 标绘请求
            
        Returns:
            List of (symbol_id, confidence_score, rendering_parameters)
        """
        
        # 1. 语义匹配
        semantic_candidates = self._semantic_symbol_search(plotting_request)
        
        # 2. 规则匹配
        rule_filtered_candidates = self._apply_cartographic_rules(semantic_candidates, plotting_request)
        
        # 3. 上下文匹配
        context_scores = self._calculate_context_scores(rule_filtered_candidates, plotting_request)
        
        # 4. 视觉适配
        visual_scores = self._calculate_visual_scores(context_scores, plotting_request)
        
        # 5. 最终排序和选择
        final_matches = self._select_best_matches(visual_scores, plotting_request)
        
        return final_matches
    
    def _semantic_symbol_search(self, plotting_request: PlottingRequest) -> List[Tuple[str, float]]:
        """基于语义相似度搜索符号"""
        query_text = f"{plotting_request.object_type} {plotting_request.object_name}"
        
        # 添加对象属性到查询
        for key, value in plotting_request.object_properties.items():
            query_text += f" {key} {value}"
        
        # 从知识库检索相关符号
        symbol_results = self.knowledge_manager.search_knowledge(
            query=query_text,
            knowledge_types=[KnowledgeType.SYMBOL_SEMANTIC],
            top_k=20
        )
        
        candidates = []
        for knowledge, score in symbol_results:
            symbol_id = knowledge.content.get('symbol_id')
            if symbol_id and symbol_id in self.symbol_registry:
                candidates.append((symbol_id, score))
        
        return candidates
    
    def _apply_cartographic_rules(self, 
                                 candidates: List[Tuple[str, float]], 
                                 plotting_request: PlottingRequest) -> List[Tuple[str, float]]:
        """应用制图规则过滤候选符号"""
        filtered_candidates = []
        
        for symbol_id, semantic_score in candidates:
            symbol_metadata = self.symbol_registry[symbol_id]
            
            # 检查是否符合制图规则
            rule_compliance = self._check_rule_compliance(symbol_metadata, plotting_request)
            
            if rule_compliance > 0.5:  # 规则符合度阈值
                adjusted_score = semantic_score * rule_compliance
                filtered_candidates.append((symbol_id, adjusted_score))
        
        return filtered_candidates
    
    def _check_rule_compliance(self, 
                              symbol_metadata: SymbolMetadata, 
                              plotting_request: PlottingRequest) -> float:
        """检查符号是否符合制图规则"""
        compliance_scores = []
        
        for rule_id, rule_info in self.cartographic_rules.items():
            rules = rule_info['rules']
            
            # 检查比例尺规则
            if 'scale_rules' in rules:
                scale_compliance = self._check_scale_compliance(
                    rules['scale_rules'], 
                    plotting_request.scale_level,
                    symbol_metadata
                )
                compliance_scores.append(scale_compliance)
            
            # 检查主题规则
            if 'theme_rules' in rules:
                theme_compliance = self._check_theme_compliance(
                    rules['theme_rules'],
                    plotting_request.map_theme,
                    symbol_metadata
                )
                compliance_scores.append(theme_compliance)
            
            # 检查对象类型规则
            if 'object_type_rules' in rules:
                type_compliance = self._check_object_type_compliance(
                    rules['object_type_rules'],
                    plotting_request.object_type,
                    symbol_metadata
                )
                compliance_scores.append(type_compliance)
        
        return sum(compliance_scores) / len(compliance_scores) if compliance_scores else 0.5
    
    def _check_scale_compliance(self, 
                               scale_rules: Dict[str, Any], 
                               scale_level: str,
                               symbol_metadata: SymbolMetadata) -> float:
        """检查比例尺规则符合度"""
        if scale_level in scale_rules:
            allowed_categories = scale_rules[scale_level].get('allowed_categories', [])
            if symbol_metadata.category.value in allowed_categories:
                return 1.0
            else:
                return 0.3
        return 0.5
    
    def _check_theme_compliance(self, 
                               theme_rules: Dict[str, Any],
                               map_theme: str,
                               symbol_metadata: SymbolMetadata) -> float:
        """检查主题规则符合度"""
        if map_theme in theme_rules:
            preferred_styles = theme_rules[map_theme].get('preferred_styles', [])
            if symbol_metadata.style.value in preferred_styles:
                return 1.0
            else:
                return 0.4
        return 0.5
    
    def _check_object_type_compliance(self, 
                                     type_rules: Dict[str, Any],
                                     object_type: str,
                                     symbol_metadata: SymbolMetadata) -> float:
        """检查对象类型规则符合度"""
        for rule_pattern, rule_config in type_rules.items():
            if rule_pattern.lower() in object_type.lower():
                required_tags = rule_config.get('required_tags', [])
                symbol_tags = symbol_metadata.semantic_tags
                
                matching_tags = set(required_tags).intersection(set(symbol_tags))
                if required_tags:
                    return len(matching_tags) / len(required_tags)
                else:
                    return 1.0
        return 0.5
    
    def _calculate_context_scores(self, 
                                 candidates: List[Tuple[str, float]], 
                                 plotting_request: PlottingRequest) -> Dict[str, float]:
        """计算上下文匹配分数"""
        context_scores = {}
        
        for symbol_id, rule_score in candidates:
            symbol_metadata = self.symbol_registry[symbol_id]
            
            # 使用上下文匹配度
            usage_context_score = self._calculate_usage_context_score(
                symbol_metadata.usage_context,
                plotting_request.context
            )
            
            # 属性匹配度
            property_score = self._calculate_property_score(
                symbol_metadata.semantic_tags,
                plotting_request.object_properties
            )
            
            # 综合上下文分数
            context_score = (
                rule_score * 0.5 +
                usage_context_score * 0.3 +
                property_score * 0.2
            )
            
            context_scores[symbol_id] = context_score
        
        return context_scores
    
    def _calculate_usage_context_score(self, 
                                      symbol_contexts: List[str],
                                      request_context: Dict[str, Any]) -> float:
        """计算使用上下文匹配分数"""
        if not symbol_contexts:
            return 0.5
        
        context_text = " ".join(str(v) for v in request_context.values())
        context_keywords = set(context_text.lower().split())
        
        max_score = 0.0
        for symbol_context in symbol_contexts:
            symbol_keywords = set(symbol_context.lower().split())
            intersection = context_keywords.intersection(symbol_keywords)
            union = context_keywords.union(symbol_keywords)
            
            if union:
                score = len(intersection) / len(union)
                max_score = max(max_score, score)
        
        return max_score
    
    def _calculate_property_score(self, 
                                 symbol_tags: List[str],
                                 object_properties: Dict[str, Any]) -> float:
        """计算属性匹配分数"""
        if not symbol_tags:
            return 0.5
        
        property_text = " ".join(f"{k} {v}" for k, v in object_properties.items())
        property_keywords = set(property_text.lower().split())
        
        symbol_keywords = set(" ".join(symbol_tags).lower().split())
        
        intersection = property_keywords.intersection(symbol_keywords)
        union = property_keywords.union(symbol_keywords)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _calculate_visual_scores(self, 
                                context_scores: Dict[str, float],
                                plotting_request: PlottingRequest) -> Dict[str, float]:
        """计算视觉适配分数"""
        visual_scores = {}
        
        for symbol_id, context_score in context_scores.items():
            symbol_metadata = self.symbol_registry[symbol_id]
            
            # 视觉属性匹配
            visual_match = self._calculate_visual_match(
                symbol_metadata.visual_properties,
                plotting_request.style_preferences
            )
            
            # 优先级权重
            priority_weight = symbol_metadata.priority / 10.0
            
            # 最终视觉分数
            visual_score = (
                context_score * 0.7 +
                visual_match * 0.2 +
                priority_weight * 0.1
            )
            
            visual_scores[symbol_id] = visual_score
        
        return visual_scores
    
    def _calculate_visual_match(self, 
                               symbol_visual: Dict[str, Any],
                               style_preferences: Dict[str, Any]) -> float:
        """计算视觉属性匹配度"""
        if not style_preferences or not symbol_visual:
            return 0.5
        
        match_count = 0
        total_count = 0
        
        for pref_key, pref_value in style_preferences.items():
            if pref_key in symbol_visual:
                total_count += 1
                symbol_value = symbol_visual[pref_key]
                
                if isinstance(pref_value, str) and isinstance(symbol_value, str):
                    if pref_value.lower() == symbol_value.lower():
                        match_count += 1
                elif pref_value == symbol_value:
                    match_count += 1
        
        return match_count / total_count if total_count > 0 else 0.5
    
    def _select_best_matches(self, 
                            visual_scores: Dict[str, float],
                            plotting_request: PlottingRequest,
                            max_matches: int = 3) -> List[Tuple[str, float, Dict[str, Any]]]:
        """选择最佳匹配符号"""
        # 按分数排序
        sorted_symbols = sorted(visual_scores.items(), key=lambda x: x[1], reverse=True)
        
        best_matches = []
        for symbol_id, score in sorted_symbols[:max_matches]:
            # 生成渲染参数
            rendering_params = self._generate_rendering_parameters(symbol_id, plotting_request)
            best_matches.append((symbol_id, score, rendering_params))
        
        return best_matches
    
    def _generate_rendering_parameters(self, 
                                     symbol_id: str,
                                     plotting_request: PlottingRequest) -> Dict[str, Any]:
        """生成渲染参数"""
        symbol_metadata = self.symbol_registry[symbol_id]
        
        # 基础渲染参数
        rendering_params = {
            'symbol_id': symbol_id,
            'category': symbol_metadata.category.value,
            'style': symbol_metadata.style.value
        }
        
        # 添加视觉属性
        rendering_params.update(symbol_metadata.visual_properties)
        
        # 根据比例尺调整大小
        if 'size' in rendering_params:
            scale_factor = self._get_scale_factor(plotting_request.scale_level)
            rendering_params['size'] = rendering_params['size'] * scale_factor
        
        # 根据主题调整颜色
        if plotting_request.map_theme and 'color' in rendering_params:
            theme_color = self._get_theme_color(plotting_request.map_theme, symbol_metadata.style)
            if theme_color:
                rendering_params['color'] = theme_color
        
        return rendering_params
    
    def _get_scale_factor(self, scale_level: str) -> float:
        """获取比例尺因子"""
        scale_factors = {
            'large': 1.5,    # 大比例尺
            'medium': 1.0,   # 中比例尺
            'small': 0.7     # 小比例尺
        }
        return scale_factors.get(scale_level, 1.0)
    
    def _get_theme_color(self, map_theme: str, symbol_style: SymbolStyle) -> Optional[str]:
        """获取主题颜色"""
        theme_colors = {
            'military': {
                SymbolStyle.MILITARY: '#8B4513',
                SymbolStyle.CIVILIAN: '#4169E1'
            },
            'civilian': {
                SymbolStyle.CIVILIAN: '#228B22',
                SymbolStyle.INFRASTRUCTURE: '#FF6347'
            }
        }
        
        return theme_colors.get(map_theme, {}).get(symbol_style)


# 使用示例
if __name__ == "__main__":
    matcher = IntelligentSymbolMatcher()
    
    request = PlottingRequest(
        object_type="港口",
        object_name="高雄港",
        object_properties={"type": "商港", "scale": "大型"},
        context={"region": "中国台湾省", "purpose": "商业标绘"},
        style_preferences={"color": "blue", "size": "medium"},
        scale_level="medium",
        map_theme="civilian"
    )
    
    matches = matcher.match_symbol(request)
    for symbol_id, score, params in matches:
        print(f"符号: {symbol_id}, 分数: {score:.3f}, 参数: {params}")
