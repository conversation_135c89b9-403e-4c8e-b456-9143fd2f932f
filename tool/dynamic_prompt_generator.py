from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json
from tool.semantic_knowledge_manager import SemanticKnowledgeManager, KnowledgeType, KnowledgeLayer


class PromptLevel(Enum):
    """Prompt层级"""
    LEVEL_0 = "level_0"  # 元Prompt，用于生成其他Prompt
    LEVEL_1 = "level_1"  # 中间Prompt，包含基本结构和规则
    LEVEL_2 = "level_2"  # 执行Prompt，直接用于LLM调用


@dataclass
class PromptContext:
    """Prompt上下文信息"""
    task_type: str
    user_input: str
    current_step: str
    available_tools: List[str]
    data_sources: List[str]
    constraints: List[str]
    examples: List[Dict[str, Any]]
    domain_knowledge: List[str]


class DynamicPromptGenerator:
    """动态Prompt生成器"""
    
    def __init__(self):
        self.knowledge_manager = SemanticKnowledgeManager()
        self.knowledge_manager.initialize()
    
    def generate_prompt(self, 
                       prompt_type: str,
                       context: PromptContext,
                       target_level: PromptLevel = PromptLevel.LEVEL_2) -> str:
        """
        动态生成Prompt
        
        Args:
            prompt_type: Prompt类型（如task_decomposition, tool_selection等）
            context: 上下文信息
            target_level: 目标Prompt层级
            
        Returns:
            生成的Prompt字符串
        """
        
        # 1. 检索相关的Prompt模板
        template_results = self.knowledge_manager.search_knowledge(
            query=f"{prompt_type} {context.task_type} {context.current_step}",
            knowledge_types=[KnowledgeType.PROMPT_TEMPLATE],
            layers=[KnowledgeLayer.META, KnowledgeLayer.DOMAIN],
            top_k=3
        )
        
        # 2. 检索相关的领域知识
        domain_results = self.knowledge_manager.search_knowledge(
            query=f"{context.task_type} {' '.join(context.domain_knowledge)}",
            knowledge_types=[KnowledgeType.CARTOGRAPHIC_RULE, KnowledgeType.WORKFLOW_PATTERN],
            top_k=5
        )
        
        # 3. 检索工具调用规则
        tool_results = self.knowledge_manager.search_knowledge(
            query=f"工具调用 {' '.join(context.available_tools)}",
            knowledge_types=[KnowledgeType.TOOL_RULE],
            top_k=3
        )
        
        # 4. 构建分层Prompt
        if target_level == PromptLevel.LEVEL_0:
            return self._generate_level_0_prompt(prompt_type, context)
        elif target_level == PromptLevel.LEVEL_1:
            return self._generate_level_1_prompt(prompt_type, context, template_results, domain_results)
        else:  # LEVEL_2
            return self._generate_level_2_prompt(prompt_type, context, template_results, domain_results, tool_results)
    
    def _generate_level_0_prompt(self, prompt_type: str, context: PromptContext) -> str:
        """生成0级元Prompt"""
        meta_prompt = f"""
你是一个专业的Prompt工程师，需要为"{prompt_type}"任务生成高质量的执行Prompt。

任务背景：
- 任务类型：{context.task_type}
- 当前步骤：{context.current_step}
- 用户输入：{context.user_input}

请生成一个结构化的Prompt，包含以下要素：
1. 角色定义和专业背景
2. 任务目标和期望输出
3. 输入数据的处理规则
4. 输出格式的具体要求
5. 约束条件和注意事项
6. 示例演示（如果需要）

生成的Prompt应该：
- 清晰明确，避免歧义
- 包含必要的上下文信息
- 符合当前任务的特定需求
- 易于LLM理解和执行
"""
        return meta_prompt
    
    def _generate_level_1_prompt(self, 
                                prompt_type: str, 
                                context: PromptContext,
                                template_results: List,
                                domain_results: List) -> str:
        """生成1级中间Prompt"""
        
        # 提取模板信息
        template_content = ""
        if template_results:
            for knowledge, score in template_results:
                template_content += f"模板参考：{knowledge.content.get('template', '')}\n"
        
        # 提取领域知识
        domain_knowledge = ""
        if domain_results:
            for knowledge, score in domain_results:
                domain_knowledge += f"领域规则：{knowledge.description}\n"
                domain_knowledge += f"具体内容：{knowledge.content}\n"
        
        level_1_prompt = f"""
基于以下信息生成"{prompt_type}"的执行Prompt：

## 任务上下文
- 任务类型：{context.task_type}
- 当前步骤：{context.current_step}
- 用户输入：{context.user_input}
- 可用工具：{', '.join(context.available_tools)}
- 数据源：{', '.join(context.data_sources)}

## 模板参考
{template_content}

## 领域知识
{domain_knowledge}

## 约束条件
{chr(10).join(context.constraints)}

请生成一个完整的执行Prompt，确保包含所有必要的指导信息。
"""
        return level_1_prompt
    
    def _generate_level_2_prompt(self, 
                                prompt_type: str,
                                context: PromptContext,
                                template_results: List,
                                domain_results: List,
                                tool_results: List) -> str:
        """生成2级执行Prompt"""
        
        # 构建基础结构
        base_structure = self._build_base_structure(prompt_type, context)
        
        # 添加领域知识
        domain_section = self._build_domain_section(domain_results)
        
        # 添加工具调用规则
        tool_section = self._build_tool_section(tool_results, context.available_tools)
        
        # 添加示例
        example_section = self._build_example_section(context.examples)
        
        # 组装最终Prompt
        final_prompt = f"""
{base_structure}

{domain_section}

{tool_section}

{example_section}

## 输入信息
用户输入：{context.user_input}
当前步骤：{context.current_step}

## 输出要求
请严格按照上述规则和格式要求进行处理，确保输出结果的准确性和完整性。
"""
        return final_prompt
    
    def _build_base_structure(self, prompt_type: str, context: PromptContext) -> str:
        """构建基础结构"""
        role_mapping = {
            "task_decomposition": "任务分解专家",
            "tool_selection": "工具选择专家", 
            "symbol_matching": "地图符号专家",
            "data_processing": "数据处理专家"
        }
        
        role = role_mapping.get(prompt_type, "专业助手")
        
        return f"""
## 角色定义
你是一位专业的{role}，具有丰富的{context.task_type}领域经验。

## 任务目标
{self._get_task_objective(prompt_type)}

## 处理原则
1. 准确理解用户需求
2. 遵循专业标准和规范
3. 确保输出结果的可执行性
4. 考虑实际应用场景的限制
"""
    
    def _get_task_objective(self, prompt_type: str) -> str:
        """获取任务目标描述"""
        objectives = {
            "task_decomposition": "将复杂任务分解为可执行的子任务序列",
            "tool_selection": "根据任务需求选择最合适的工具和方法",
            "symbol_matching": "为地图标绘选择最合适的符号和样式",
            "data_processing": "对输入数据进行清洗、转换和结构化处理"
        }
        return objectives.get(prompt_type, "完成指定的专业任务")
    
    def _build_domain_section(self, domain_results: List) -> str:
        """构建领域知识部分"""
        if not domain_results:
            return ""
        
        section = "## 领域知识和规则\n"
        for knowledge, score in domain_results:
            section += f"### {knowledge.title}\n"
            section += f"{knowledge.description}\n"
            if isinstance(knowledge.content, dict):
                for key, value in knowledge.content.items():
                    section += f"- {key}: {value}\n"
            section += "\n"
        
        return section
    
    def _build_tool_section(self, tool_results: List, available_tools: List[str]) -> str:
        """构建工具调用部分"""
        if not tool_results and not available_tools:
            return ""
        
        section = "## 可用工具和调用规则\n"
        section += f"当前可用工具：{', '.join(available_tools)}\n\n"
        
        for knowledge, score in tool_results:
            section += f"### {knowledge.title}\n"
            section += f"{knowledge.description}\n"
            if 'usage' in knowledge.content:
                section += f"使用方法：{knowledge.content['usage']}\n"
            if 'parameters' in knowledge.content:
                section += f"参数说明：{knowledge.content['parameters']}\n"
            section += "\n"
        
        return section
    
    def _build_example_section(self, examples: List[Dict[str, Any]]) -> str:
        """构建示例部分"""
        if not examples:
            return ""
        
        section = "## 示例参考\n"
        for i, example in enumerate(examples, 1):
            section += f"### 示例 {i}\n"
            section += f"输入：{example.get('input', '')}\n"
            section += f"输出：{example.get('output', '')}\n"
            if 'explanation' in example:
                section += f"说明：{example['explanation']}\n"
            section += "\n"
        
        return section


# 使用示例
if __name__ == "__main__":
    generator = DynamicPromptGenerator()
    
    context = PromptContext(
        task_type="地图标绘",
        user_input="标绘中国台湾省港口数据",
        current_step="任务分解",
        available_tools=["数据库查询", "向量搜索", "地图渲染"],
        data_sources=["港口数据库", "地理信息库"],
        constraints=["确保数据准确性", "符合制图规范"],
        examples=[],
        domain_knowledge=["港口分类", "地图符号"]
    )
    
    prompt = generator.generate_prompt("task_decomposition", context)
    print(prompt)
