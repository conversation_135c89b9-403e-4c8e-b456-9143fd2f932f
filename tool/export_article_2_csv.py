from datetime import datetime, timedelta, UTC
import csv
from elasticsearch import Elasticsearch
import time
import os


def export_articles_to_csv(es_hosts=["http://localhost:9200"],
                           index_name="article_data2",
                           days_back=15,
                           output_file="../data/knowledge_data/news_knowledge/news_output.csv",
                           id_file="../data/knowledge_data/news_knowledge/exported_ids.txt",
                           output_fields=["_id", "title", "create_time", "source_name", "content"]):
    """
    增量导出ES数据到CSV
    参数：
    - es_hosts: ES连接地址列表
    - index_name: 索引名称
    - days_back: 查询时间范围（天数）
    - output_file: 输出CSV文件路径
    - id_file: 已导出ID记录文件路径
    - output_fields: 指定导出的字段列表（必须包含所有需要导出的字段名）
    """
    # 初始化ES连接
    es = Elasticsearch(hosts=es_hosts)

    # 读取已导出ID
    existing_ids = set()
    if os.path.exists(id_file):
        with open(id_file, 'r') as f:
            existing_ids = set(f.read().splitlines())

    # 构建时间范围
    current_time = int(time.time())
    time_threshold = current_time - days_back * 24 * 60 * 60

    # 构建增量查询DSL
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "terms": {
                            "source_name.keyword": [
                                "新华国际头条", "人民日报", "观察者网", "环球时报",
                                "环球网", "CHINADAILY", "国际在线", "人民日报国际", "参考消息"
                                #, "遥瞰天下" "巅峰高地","爱国尚武","世界舰队","中兵智库","天外风云","数字海洋与水下攻防","装备参考","央视军事","无人争锋","军鹰动态","战舰一号","海鹰资讯","东海舰队发布","北海舰队","中国海军陆战队","人民海军","知远战略与防务研究所"

                            ]
                        }
                    },
                    {
                        "range": {
                            "create_time": {
                                "gte": time_threshold,
                                "lte": current_time
                            }
                        }
                    }
                ],
                "must_not": [
                    {
                        "ids": {
                            "values": list(existing_ids)
                        }
                    }
                ]
            }
        },
        "_source": ["*"],
        "size": 10000
    }

    # 执行查询
    response = es.search(index=index_name, body=query)
    new_records = response['hits']['hits']

    if not new_records:
        print("没有新数据需要导出")
        return

    # 写入CSV文件
    file_exists = os.path.exists(output_file)
    with open(output_file, 'a' if file_exists else 'w', newline='', encoding='utf-8-sig') as f:
        writer = None
        new_ids = []

        for hit in new_records:
            doc_sorurce = hit["_source"]
            doc = {}
            doc_id = hit["_id"]
            if output_fields:
                for field in output_fields:
                    if field in doc_sorurce:
                        doc[field] = doc_sorurce[field]
            else:
                doc = doc_sorurce
            doc["_id"] = doc_id

            # 处理时间字段
            if "create_time" in doc:
                try:
                    dt = datetime.fromtimestamp(doc["create_time"], tz=UTC) + timedelta(hours=8)
                    doc["新闻时间"] = dt.strftime("%Y-%m-%d")
                except Exception as e:
                    print(f"时间转换错误：{e}")
                    doc["新闻时间"] = "无效时间"
            else:
                doc["新闻时间"] = "无时间字段"

            # 初始化写入器
            if not writer:
                # 读取已有字段或创建新字段
                if file_exists:
                    with open(output_file, 'r', encoding='utf-8-sig') as header_file:
                        reader = csv.DictReader(header_file)
                        fieldnames = reader.fieldnames
                else:
                    fieldnames = sorted(doc.keys())

                writer = csv.DictWriter(f, fieldnames=fieldnames)
                if not file_exists:
                    writer.writeheader()

            # 写入数据
            writer.writerow(doc)
            new_ids.append(doc_id)

        # 更新已导出ID记录
        if new_ids:
            with open(id_file, 'a') as f:
                f.write('\n'.join(new_ids) + '\n')

    print(f"新增 {len(new_records)} 条记录，总数据量：{len(existing_ids) + len(new_records)}")


# 使用示例
if __name__ == "__main__":
    export_articles_to_csv()
