from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from tool.semantic_knowledge_manager import SemanticKnowledgeManager, KnowledgeType, KnowledgeLayer


class ToolCategory(Enum):
    """工具类别"""
    DATA_RETRIEVAL = "data_retrieval"      # 数据检索
    DATA_PROCESSING = "data_processing"    # 数据处理
    VISUALIZATION = "visualization"        # 可视化
    ANALYSIS = "analysis"                  # 分析
    COMMUNICATION = "communication"        # 通信
    UTILITY = "utility"                    # 工具类


@dataclass
class ToolCapability:
    """工具能力描述"""
    tool_name: str
    category: ToolCategory
    description: str
    input_types: List[str]
    output_types: List[str]
    parameters: Dict[str, Any]
    constraints: List[str]
    performance_metrics: Dict[str, float]
    usage_examples: List[Dict[str, Any]]


@dataclass
class TaskRequirement:
    """任务需求描述"""
    task_type: str
    input_data_type: str
    expected_output_type: str
    performance_requirements: Dict[str, float]
    constraints: List[str]
    context: Dict[str, Any]


class IntelligentToolSelector:
    """智能工具选择器"""
    
    def __init__(self):
        self.knowledge_manager = SemanticKnowledgeManager()
        self.knowledge_manager.initialize()
        self.tool_registry = {}
        self._load_tool_registry()
    
    def _load_tool_registry(self):
        """加载工具注册表"""
        # 从语义知识库中加载工具信息
        tool_results = self.knowledge_manager.search_knowledge(
            query="工具调用规则 API规格",
            knowledge_types=[KnowledgeType.TOOL_RULE],
            top_k=50
        )
        
        for knowledge, score in tool_results:
            tool_info = knowledge.content
            if 'tool_name' in tool_info:
                self.tool_registry[tool_info['tool_name']] = ToolCapability(
                    tool_name=tool_info['tool_name'],
                    category=ToolCategory(tool_info.get('category', 'utility')),
                    description=tool_info.get('description', ''),
                    input_types=tool_info.get('input_types', []),
                    output_types=tool_info.get('output_types', []),
                    parameters=tool_info.get('parameters', {}),
                    constraints=tool_info.get('constraints', []),
                    performance_metrics=tool_info.get('performance_metrics', {}),
                    usage_examples=tool_info.get('usage_examples', [])
                )
    
    def select_tools(self, 
                    task_requirement: TaskRequirement,
                    max_tools: int = 3) -> List[Tuple[str, float, Dict[str, Any]]]:
        """
        智能选择工具
        
        Args:
            task_requirement: 任务需求
            max_tools: 最大工具数量
            
        Returns:
            List of (tool_name, confidence_score, recommended_parameters)
        """
        
        # 1. 基于语义相似度的初步筛选
        semantic_candidates = self._semantic_tool_search(task_requirement)
        
        # 2. 基于能力匹配的精确筛选
        capability_scores = self._calculate_capability_scores(semantic_candidates, task_requirement)
        
        # 3. 基于性能和约束的最终排序
        final_scores = self._calculate_final_scores(capability_scores, task_requirement)
        
        # 4. 选择最佳工具组合
        selected_tools = self._select_optimal_combination(final_scores, task_requirement, max_tools)
        
        return selected_tools
    
    def _semantic_tool_search(self, task_requirement: TaskRequirement) -> List[Tuple[str, float]]:
        """基于语义相似度搜索工具"""
        query = f"{task_requirement.task_type} {task_requirement.input_data_type} {task_requirement.expected_output_type}"
        
        # 从知识库检索相关工具
        tool_results = self.knowledge_manager.search_knowledge(
            query=query,
            knowledge_types=[KnowledgeType.TOOL_RULE],
            top_k=10
        )
        
        candidates = []
        for knowledge, score in tool_results:
            tool_name = knowledge.content.get('tool_name')
            if tool_name and tool_name in self.tool_registry:
                candidates.append((tool_name, score))
        
        return candidates
    
    def _calculate_capability_scores(self, 
                                   candidates: List[Tuple[str, float]], 
                                   task_requirement: TaskRequirement) -> Dict[str, float]:
        """计算工具能力匹配分数"""
        capability_scores = {}
        
        for tool_name, semantic_score in candidates:
            tool_capability = self.tool_registry[tool_name]
            
            # 输入类型匹配度
            input_match = self._calculate_type_match(
                task_requirement.input_data_type, 
                tool_capability.input_types
            )
            
            # 输出类型匹配度
            output_match = self._calculate_type_match(
                task_requirement.expected_output_type,
                tool_capability.output_types
            )
            
            # 约束满足度
            constraint_satisfaction = self._calculate_constraint_satisfaction(
                task_requirement.constraints,
                tool_capability.constraints
            )
            
            # 综合能力分数
            capability_score = (
                semantic_score * 0.3 +
                input_match * 0.25 +
                output_match * 0.25 +
                constraint_satisfaction * 0.2
            )
            
            capability_scores[tool_name] = capability_score
        
        return capability_scores
    
    def _calculate_type_match(self, required_type: str, available_types: List[str]) -> float:
        """计算类型匹配度"""
        if not available_types:
            return 0.0
        
        # 精确匹配
        if required_type in available_types:
            return 1.0
        
        # 模糊匹配（基于关键词）
        required_keywords = set(required_type.lower().split())
        max_match = 0.0
        
        for available_type in available_types:
            available_keywords = set(available_type.lower().split())
            intersection = required_keywords.intersection(available_keywords)
            union = required_keywords.union(available_keywords)
            
            if union:
                match_score = len(intersection) / len(union)
                max_match = max(max_match, match_score)
        
        return max_match
    
    def _calculate_constraint_satisfaction(self, 
                                         task_constraints: List[str], 
                                         tool_constraints: List[str]) -> float:
        """计算约束满足度"""
        if not task_constraints:
            return 1.0
        
        satisfied_count = 0
        for task_constraint in task_constraints:
            # 检查工具是否能满足任务约束
            if self._constraint_satisfied(task_constraint, tool_constraints):
                satisfied_count += 1
        
        return satisfied_count / len(task_constraints)
    
    def _constraint_satisfied(self, task_constraint: str, tool_constraints: List[str]) -> bool:
        """检查单个约束是否满足"""
        # 简化的约束匹配逻辑
        task_keywords = set(task_constraint.lower().split())
        
        for tool_constraint in tool_constraints:
            tool_keywords = set(tool_constraint.lower().split())
            if task_keywords.intersection(tool_keywords):
                return True
        
        return False
    
    def _calculate_final_scores(self, 
                               capability_scores: Dict[str, float],
                               task_requirement: TaskRequirement) -> Dict[str, float]:
        """计算最终分数（包含性能考虑）"""
        final_scores = {}
        
        for tool_name, capability_score in capability_scores.items():
            tool_capability = self.tool_registry[tool_name]
            
            # 性能匹配度
            performance_score = self._calculate_performance_score(
                task_requirement.performance_requirements,
                tool_capability.performance_metrics
            )
            
            # 最终分数
            final_score = capability_score * 0.7 + performance_score * 0.3
            final_scores[tool_name] = final_score
        
        return final_scores
    
    def _calculate_performance_score(self, 
                                   required_performance: Dict[str, float],
                                   tool_performance: Dict[str, float]) -> float:
        """计算性能匹配分数"""
        if not required_performance or not tool_performance:
            return 0.5  # 默认中等分数
        
        total_score = 0.0
        metric_count = 0
        
        for metric, required_value in required_performance.items():
            if metric in tool_performance:
                tool_value = tool_performance[metric]
                
                # 根据指标类型计算分数
                if metric in ['accuracy', 'precision', 'recall']:
                    # 越高越好的指标
                    score = min(tool_value / required_value, 1.0)
                elif metric in ['latency', 'memory_usage']:
                    # 越低越好的指标
                    score = min(required_value / tool_value, 1.0)
                else:
                    # 默认处理
                    score = 1.0 - abs(tool_value - required_value) / max(tool_value, required_value)
                
                total_score += max(score, 0.0)
                metric_count += 1
        
        return total_score / metric_count if metric_count > 0 else 0.5
    
    def _select_optimal_combination(self, 
                                  final_scores: Dict[str, float],
                                  task_requirement: TaskRequirement,
                                  max_tools: int) -> List[Tuple[str, float, Dict[str, Any]]]:
        """选择最优工具组合"""
        # 按分数排序
        sorted_tools = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        selected_tools = []
        for tool_name, score in sorted_tools[:max_tools]:
            # 生成推荐参数
            recommended_params = self._generate_recommended_parameters(
                tool_name, task_requirement
            )
            
            selected_tools.append((tool_name, score, recommended_params))
        
        return selected_tools
    
    def _generate_recommended_parameters(self, 
                                       tool_name: str,
                                       task_requirement: TaskRequirement) -> Dict[str, Any]:
        """生成推荐参数"""
        tool_capability = self.tool_registry[tool_name]
        recommended_params = {}
        
        # 基于任务需求和工具能力生成参数
        for param_name, param_info in tool_capability.parameters.items():
            if isinstance(param_info, dict):
                param_type = param_info.get('type', 'string')
                default_value = param_info.get('default')
                
                # 根据任务上下文调整参数
                if param_name in task_requirement.context:
                    recommended_params[param_name] = task_requirement.context[param_name]
                elif default_value is not None:
                    recommended_params[param_name] = default_value
        
        return recommended_params
    
    def get_tool_usage_guidance(self, tool_name: str) -> Dict[str, Any]:
        """获取工具使用指导"""
        if tool_name not in self.tool_registry:
            return {}
        
        tool_capability = self.tool_registry[tool_name]
        
        # 从知识库检索详细的使用指导
        guidance_results = self.knowledge_manager.search_knowledge(
            query=f"{tool_name} 使用方法 调用示例",
            knowledge_types=[KnowledgeType.TOOL_RULE],
            top_k=3
        )
        
        guidance = {
            'description': tool_capability.description,
            'parameters': tool_capability.parameters,
            'examples': tool_capability.usage_examples,
            'constraints': tool_capability.constraints
        }
        
        # 添加从知识库检索的详细指导
        if guidance_results:
            detailed_guidance = []
            for knowledge, score in guidance_results:
                if 'usage_guidance' in knowledge.content:
                    detailed_guidance.append(knowledge.content['usage_guidance'])
            
            guidance['detailed_guidance'] = detailed_guidance
        
        return guidance


# 使用示例
if __name__ == "__main__":
    selector = IntelligentToolSelector()
    
    task_req = TaskRequirement(
        task_type="地图标绘",
        input_data_type="港口数据",
        expected_output_type="地图可视化",
        performance_requirements={'accuracy': 0.95, 'latency': 2.0},
        constraints=["实时处理", "高精度"],
        context={"region": "中国台湾省", "data_source": "数据库"}
    )
    
    selected_tools = selector.select_tools(task_req)
    for tool_name, score, params in selected_tools:
        print(f"工具: {tool_name}, 分数: {score:.3f}, 参数: {params}")
