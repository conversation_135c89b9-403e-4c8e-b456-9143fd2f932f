import os
import faiss
import numpy as np
import pandas as pd
from model.azure_client import AzureClient
import time

class VectorSearch:
    def __init__(self, index_file_path, metadata_file_path):
        self.index = None
        self.metadata = None
        self.id_map = None  # 存储向量ID与元数据索引的映射
        self.index_file_path = index_file_path
        self.metadata_file_path = metadata_file_path

    def initialize(self):
        print(f"[{time.strftime('%H:%M:%S')}] 开始加载索引和元数据...")  # 新增开始时间打印
        start_time = time.time()
        """加载索引和元数据"""
        self.metadata = pd.read_parquet(self.metadata_file_path)
        self.index = faiss.read_index(self.index_file_path)

        # 构建ID映射（假设索引顺序与元数据顺序一致）
        self.id_map = np.arange(self.metadata.shape[0]).astype('int64')
        end_time = time.time()  # 新增结束时间记录
        print(f"[{time.strftime('%H:%M:%S')}] 加载完成，耗时 {end_time - start_time:.2f} 秒")  # 新增耗时打印

    def _apply_filters(self, filters):
        """
        应用元数据过滤器
        :param filters: 过滤条件列表，每个条件为字典格式
           示例: [{'field': '新闻时间', 'op': '>=', 'value': '2023-01-01'}]
           op支持: ==, !=, >, >=, <, <=, contains
        """
        df = self.metadata.copy()

        for condition in filters:
            field = condition['field']
            op = condition['op']
            value = condition['value']

            if op == '==':
                df = df[df[field] == value]
            elif op == '!=':
                df = df[df[field] != value]
            elif op == '>':
                df = df[df[field] > value]
            elif op == '>=':
                df = df[df[field] >= value]
            elif op == '<':
                df = df[df[field] < value]
            elif op == '<=':
                df = df[df[field] <= value]
            elif op == 'contains':
                df = df[df[field].str.contains(value, na=False)]
            elif op == 'in':
                if not isinstance(value, (list, tuple, set)):
                    raise ValueError(f"'in'操作符需要列表类型值，当前收到类型：{type(value)}")
                df = df[df[field].isin(value)]
            else:
                raise ValueError(f"不支持的运算符: {op}")

        return df.index.values.astype('int64')  # 返回原始数据索引

    def filtered_search(self, query_text, filters=None, top_k=5):
        """
        增强版过滤搜索（避免重建索引）
        :param filters: 过滤条件列表
        """
        if self.index is None:
            self.initialize()

        # 获取查询向量
        query_embedding = AzureClient.get_embeddings([query_text])[0]
        query_vector = np.array(query_embedding).astype('float32').reshape(1, -1)
        faiss.normalize_L2(query_vector)

        # 应用过滤条件
        if filters:
            valid_ids = self._apply_filters(filters)
            if len(valid_ids) == 0:
                return []

            # 创建ID选择器
            valid_ids = np.array(valid_ids, dtype='int64')
            sel = faiss.IDSelectorBatch(
                valid_ids.size,
                faiss.swig_ptr(valid_ids)
            )  # 新版本不需要第三个参数
        else:
            sel = None

        # 执行带过滤的搜索
        distances, indices = self.index.search(
            query_vector,
            top_k,
            params=faiss.SearchParameters(sel=sel)
        )

        # 转换结果
        meta_results = []
        text_results = []
        for i in range(top_k):
            if indices[0][i] == -1:
                continue

            original_idx = self.id_map[indices[0][i]]  # 映射回元数据索引
            score = float(distances[0][i]),
            metadata = self.metadata.iloc[original_idx].to_dict()

            meta_results.append({"metadata": metadata, "score": score[0]})

            text_results.append(metadata['combined_text'])

        return meta_results, text_results




# 使用示例
if __name__ == "__main__":
    # process_csv(csv_path, os.path.join(VECTOR_DIR, "news_metadata.parquet"))
    # 获取当前文件所在目录的绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 回退到项目根目录（根据实际目录层级调整）
    project_root = os.path.dirname(current_dir)

    VECTOR_DIR = os.path.join(project_root, "data", "knowledge_data", 'airport_knowledge')
    index_file_path = os.path.join(VECTOR_DIR, "airports_index.index")
    metadata_file_path = os.path.join(VECTOR_DIR, "airports_metadata.parquet")
    # print(f"[{time.strftime('%H:%M:%S')}]")  # 新增开始时间打印
    # start_time = time.time()
    # searcher = VectorSearch(index_file_path, metadata_file_path)
    # searcher.initialize()
    #
    # # 示例2：带时间过滤的搜索
    # filters = [
    #     {
    #         'field': '新闻时间',
    #         'op': '>=',
    #         'value': '2025-04-25'  # 搜索2023年之后的新闻
    #     }
    # ]
    # results = searcher.filtered_search(
    #     "适用于飞机的图标",
    #     filters=None,
    #     top_k=4
    # )
    #
    # end_time = time.time()  # 新增结束时间记录
    # print(f"[{time.strftime('%H:%M:%S')}] 查询完成，耗时 {end_time - start_time:.2f} 秒")  # 新增耗时打印
    #
    # # 结果解析
    # for result in results[1]:
    #     print(result)

    searcher = VectorSearch(index_file_path, metadata_file_path)
    searcher.initialize()
    results = searcher.filtered_search(
        "美国机场数据",
        filters=None,
        top_k=100
    )
    for result in results[0]:
        print(result['score'])
        print(result['metadata'])
        print("****************")