import os
import json
import uuid
from typing import Dict, List, Any, Optional, Tu<PERSON>
from dataclasses import dataclass
from enum import Enum
import numpy as np
import faiss
import pandas as pd
from model.azure_client import AzureClient
from tool.file_tool import FileUtils


class KnowledgeType(Enum):
    """知识类型枚举"""
    PROMPT_TEMPLATE = "prompt_template"
    TOOL_RULE = "tool_rule"
    SYMBOL_SEMANTIC = "symbol_semantic"
    CARTOGRAPHIC_RULE = "cartographic_rule"
    WORKFLOW_PATTERN = "workflow_pattern"
    DATA_SOURCE_SPEC = "data_source_spec"
    ERROR_HANDLING = "error_handling"


class KnowledgeLayer(Enum):
    """知识层级枚举"""
    META = "meta_layer"          # 元知识层
    DOMAIN = "domain_layer"      # 领域知识层
    EXECUTION = "execution_layer" # 执行知识层


@dataclass
class SemanticKnowledge:
    """语义知识条目"""
    id: str
    knowledge_type: KnowledgeType
    layer: KnowledgeLayer
    title: str
    description: str
    content: Dict[str, Any]
    tags: List[str]
    usage_context: List[str]
    priority: int = 1
    version: str = "1.0"
    created_at: str = ""
    updated_at: str = ""


class SemanticKnowledgeManager:
    """语义知识库管理器"""
    
    def __init__(self, knowledge_dir: str = "semantic_knowledge"):
        self.knowledge_dir = os.path.join(FileUtils.get_vector_dir(), knowledge_dir)
        self.index_file = os.path.join(self.knowledge_dir, "semantic_knowledge.index")
        self.metadata_file = os.path.join(self.knowledge_dir, "semantic_knowledge.parquet")
        self.raw_data_file = os.path.join(self.knowledge_dir, "knowledge_raw.json")
        
        # 确保目录存在
        os.makedirs(self.knowledge_dir, exist_ok=True)
        
        self.index = None
        self.metadata = None
        self.knowledge_data = {}
        
    def initialize(self):
        """初始化知识库"""
        if os.path.exists(self.index_file) and os.path.exists(self.metadata_file):
            self.index = faiss.read_index(self.index_file)
            self.metadata = pd.read_parquet(self.metadata_file)
            
        if os.path.exists(self.raw_data_file):
            with open(self.raw_data_file, 'r', encoding='utf-8') as f:
                self.knowledge_data = json.load(f)
    
    def add_knowledge(self, knowledge: SemanticKnowledge) -> bool:
        """添加知识条目"""
        try:
            # 生成嵌入向量
            combined_text = f"{knowledge.title} {knowledge.description} {' '.join(knowledge.tags)}"
            embedding = AzureClient.get_embedding(combined_text)
            
            # 准备元数据
            metadata_row = {
                'id': knowledge.id,
                'knowledge_type': knowledge.knowledge_type.value,
                'layer': knowledge.layer.value,
                'title': knowledge.title,
                'description': knowledge.description,
                'tags': json.dumps(knowledge.tags, ensure_ascii=False),
                'usage_context': json.dumps(knowledge.usage_context, ensure_ascii=False),
                'priority': knowledge.priority,
                'version': knowledge.version,
                'combined_text': combined_text
            }
            
            # 更新向量索引
            if self.index is None:
                self.index = faiss.IndexFlatIP(len(embedding))
                self.metadata = pd.DataFrame([metadata_row])
            else:
                self.index.add(np.array([embedding]).astype('float32'))
                new_row = pd.DataFrame([metadata_row])
                self.metadata = pd.concat([self.metadata, new_row], ignore_index=True)
            
            # 保存原始数据
            self.knowledge_data[knowledge.id] = knowledge.__dict__
            
            # 持久化
            self._save_to_disk()
            return True
            
        except Exception as e:
            print(f"添加知识条目失败: {e}")
            return False
    
    def search_knowledge(self, 
                        query: str, 
                        knowledge_types: List[KnowledgeType] = None,
                        layers: List[KnowledgeLayer] = None,
                        top_k: int = 5) -> List[Tuple[SemanticKnowledge, float]]:
        """搜索知识条目"""
        if self.index is None:
            self.initialize()
            
        if self.index is None:
            return []
        
        # 生成查询向量
        query_embedding = AzureClient.get_embedding(query)
        query_vector = np.array([query_embedding]).astype('float32')
        faiss.normalize_L2(query_vector)
        
        # 构建过滤条件
        filters = []
        if knowledge_types:
            type_values = [kt.value for kt in knowledge_types]
            filters.append({'field': 'knowledge_type', 'op': 'in', 'value': type_values})
            
        if layers:
            layer_values = [layer.value for layer in layers]
            filters.append({'field': 'layer', 'op': 'in', 'value': layer_values})
        
        # 应用过滤
        valid_indices = self._apply_filters(filters) if filters else None
        
        # 执行搜索
        if valid_indices is not None:
            # 创建ID选择器进行过滤搜索
            valid_ids = np.array(valid_indices, dtype='int64')
            sel = faiss.IDSelectorBatch(valid_ids.size, faiss.swig_ptr(valid_ids))
            distances, indices = self.index.search(query_vector, top_k, 
                                                 params=faiss.SearchParameters(sel=sel))
        else:
            distances, indices = self.index.search(query_vector, top_k)
        
        # 构建结果
        results = []
        for i in range(top_k):
            if indices[0][i] == -1:
                continue
                
            idx = indices[0][i]
            score = float(distances[0][i])
            metadata_row = self.metadata.iloc[idx]
            knowledge_id = metadata_row['id']
            
            if knowledge_id in self.knowledge_data:
                knowledge_dict = self.knowledge_data[knowledge_id]
                knowledge = SemanticKnowledge(**knowledge_dict)
                results.append((knowledge, score))
        
        return results
    
    def _apply_filters(self, filters: List[Dict]) -> List[int]:
        """应用过滤条件"""
        df = self.metadata.copy()
        
        for condition in filters:
            field = condition['field']
            op = condition['op']
            value = condition['value']
            
            if op == '==':
                df = df[df[field] == value]
            elif op == 'in':
                df = df[df[field].isin(value)]
            elif op == 'contains':
                df = df[df[field].str.contains(value, na=False)]
        
        return df.index.values.astype('int64').tolist()
    
    def _save_to_disk(self):
        """保存到磁盘"""
        if self.index is not None:
            faiss.write_index(self.index, self.index_file)
        
        if self.metadata is not None:
            self.metadata.to_parquet(self.metadata_file)
            
        with open(self.raw_data_file, 'w', encoding='utf-8') as f:
            # 转换SemanticKnowledge对象为可序列化的字典
            serializable_data = {}
            for k, v in self.knowledge_data.items():
                if isinstance(v, dict):
                    serializable_data[k] = v
                else:
                    # 如果是SemanticKnowledge对象，转换为字典
                    serializable_data[k] = v.__dict__ if hasattr(v, '__dict__') else v
            json.dump(serializable_data, f, ensure_ascii=False, indent=2)
