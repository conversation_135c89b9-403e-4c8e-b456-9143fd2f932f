import json

import pandas as pd
import requests
from bs4 import BeautifulSoup

from tool.llm_tool import get_not_reason_llm_response_with_retry


def generate_detail_info():
    df = pd.read_excel('airport_output_result.xlsx')
    target_fields = [
        '中文名', '英文名', '所在洲', '所在国家', '所在省\州', '所在城市',
        '详细所在地', '经度', '纬度', '地理位置', '海拔高度', '机场类型',
        'IATA机场代码', 'ICAO机场代码', '机场规模', '运营信息', '详细信息'
    ]
    # 初始化新列（避免KeyError）
    for field in target_fields:
        if field not in df.columns:
            df[field] = None
    #遍历df
    for index, row in df.iterrows():
        airport_info = row.to_dict()
        prompt = f'''
请根据给出的机场信息，以及大模型自带的信息，按照严格的JSON形式返回该机场的下列字段信息。
字段要求：
1、包含中文名、英文名、所在洲、所在国家、所在省\州、所在城市、详细所在地、经度、纬度、位置描述、海拔高度、机场类型（取值范围：军用、民用、军民两用）、IATA机场代码、ICAO机场代码、机场规模、运营信息、详细信息
2、各字段要求
-中文名：机场的简体中文名称
-英文名：机场的英文名称
-所在洲：机场所在的大洲名称
-所在国家：机场所在的国家名称
-所在省\州：机场所在的国家内的省或州名称
-所在城市：机场所在的城市名称
-详细所在地：机场所在洲、所在国家、所在省\州、所在城市字段通过-拼接而来
-经度：机场所在地的经度，为浮点类型
-纬度：机场所在地的纬度，为浮点类型
-位置描述：机场所在地的地理位置描述，如“中国北京市朝阳区”
-海拔高度：机场所在地的海拔高度，单位为米
-机场类型：机场的类型，取值范围：军用、民用、军民两用
-IATA机场代码：机场的IATA机场代码，如“PEK”
-ICAO机场代码：机场的ICAO机场代码，如“ZBAA”
-机场规模：机场的规模，如“大型机场”
-运营信息：机场的运营信息，如“中国民航局运营”
-详细信息：机场的详细信息


输出要求：
1、以严格的JSON格式输出以上的机场信息，JSON字段名严格和上面的字段名保持一致。
2、直接输出最终的JSON数据，不需要任何解释和说明，也不需要```json等标记。
3、如果机场信息中存在缺失字段，请用“无”填充

给出的机场信息:
        '''
        #将json数据写入prompt
        prompt += str(airport_info)
        result = get_not_reason_llm_response_with_retry(prompt)
        print(result)
        try:
            # 解析JSON结果
            json_data = json.loads(result)

            # 更新数据到当前行
            for key in target_fields:
                df.at[index, key] = json_data.get(key, '无')

        except (json.JSONDecodeError, AttributeError) as e:
            print(f"行 {index} 解析失败: {str(e)}")
        # 保存结构化结果到新Excel
    df.to_excel('airport_structured_output_origin.xlsx',
                index=False,
                engine='openpyxl')  # 确保支持xlsx格式


def modify_airport_scale():
    # 定义判断逻辑的函数
    df = pd.read_excel('airport_structured_output.xlsx')
    def set_scale(row):
        type_str = str(row['type']).lower()  # 转换为小写确保匹配稳定性
        if 'small' in type_str:
            return '小型'
        elif 'mid' in type_str:
            return '中型'
        elif 'major' in type_str:
            return '大型'
        else:
            return row['机场规模']  # 保持原值或返回默认值

    # 应用判断逻辑
    df['机场规模'] = df.apply(set_scale, axis=1)
    # 保存结构化结果到新Excel
    df.to_excel('airport_structured_output.xlsx', index=False, engine='openpyxl')  # 确保支持xlsx格式

if __name__ == '__main__':
    # generate_detail_info()
    modify_airport_scale()


