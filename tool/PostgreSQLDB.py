import psycopg2
import threading
from typing import List, Dict, Any, Optional, Tuple
from psycopg2 import sql, extensions

from template_config.constants_config import POSTGRESQL_HOST, POSTGRESQL_DB_NAME, POSTGRESQL_PASSWORD, POSTGRESQL_USER, \
    POSTGRESQL_PORT


class PostgreSQLDB:
    """PostgreSQL数据库工具类，提供连接复用机制和完整的CRUD功能"""

    _instance_lock = threading.Lock()
    _connections = {}

    def __new__(cls):
        """单例模式实现，确保全局唯一连接池"""
        if not hasattr(cls, '_instance'):
            with cls._instance_lock:
                if not hasattr(cls, '_instance'):
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化连接池配置"""
        # 从配置文件或环境变量获取连接信息
        self.db_config = {
            "host": POSTGRESQL_HOST,
            "port": POSTGRESQL_PORT,
            "user": POSTGRESQL_USER,
            "password": POSTGRESQL_PASSWORD,
            "dbname": POSTGRESQL_DB_NAME
        }
        self._pool_lock = threading.Lock()

    def _get_connection(self) -> psycopg2.extensions.connection:
        """获取数据库连接（线程安全）"""
        thread_id = threading.get_ident()

        with self._pool_lock:
            if thread_id not in self._connections:
                dsn = f"host={self.db_config['host']} " \
                      f"port={self.db_config['port']} " \
                      f"user={self.db_config['user']} " \
                      f"password={self.db_config['password']} " \
                      f"dbname={self.db_config['dbname']}"

                conn = psycopg2.connect(dsn)
                conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
                conn.set_client_encoding('UTF8')
                self._connections[thread_id] = conn
        return self._connections[thread_id]

    def execute_query(self, query: str, params: Optional[Tuple] = None) -> Dict:
        """
        执行SQL查询（自动管理事务）

        Args:
            query: SQL语句
            params: 参数元组

        Returns:
            Dict: 包含执行结果的字典，包含 'rows' 和 'rowcount'
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # 获取查询结果
            rows = cursor.fetchall()
            description = cursor.description

            result = {
                "rows": [dict(zip([desc[0] for desc in description], row)) for row in rows],
                "rowcount": cursor.rowcount,
                "lastrowid": None  # PostgreSQL 不支持 lastrowid
            }

            conn.commit()
            return result

        except psycopg2.Error as e:
            print(f"数据库错误: {e}")
            conn.rollback()
            raise
        finally:
            cursor.close()

    def fetch_all(self, query: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        获取所有记录

        Args:
            query: SQL查询语句
            params: 参数元组

        Returns:
            List[Dict]: 查询结果字典列表
        """
        result = self.execute_query(query, params)
        return result["rows"]

    def fetch_one(self, query: str, params: Optional[Tuple] = None) -> Optional[Dict[str, Any]]:
        """
        获取单条记录

        Args:
            query: SQL查询语句
            params: 参数元组

        Returns:
            Optional[Dict]: 单条记录字典或None
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            row = cursor.fetchone()
            if row:
                description = cursor.description
                return dict(zip([desc[0] for desc in description], row))
            return None

        except psycopg2.Error as e:
            print(f"数据库错误: {e}")
            conn.rollback()
            raise
        finally:
            cursor.close()

    def execute(self, query: str, params: Optional[Tuple] = None) -> int:
        """
        执行写入操作（INSERT/UPDATE/DELETE）

        Args:
            query: SQL语句
            params: 参数元组

        Returns:
            int: 受影响行数
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            conn.commit()
            return cursor.rowcount

        except psycopg2.Error as e:
            print(f"数据库错误: {e}")
            conn.rollback()
            raise
        finally:
            cursor.close()

    def close_all(self) -> None:
        """关闭所有连接池中的连接"""
        with self._pool_lock:
            for conn in self._connections.values():
                try:
                    conn.close()
                except Exception:
                    pass
            self._connections.clear()

    @staticmethod
    def query_with_sql(sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        db = PostgreSQLDB()
        return db.fetch_all(sql, params)
