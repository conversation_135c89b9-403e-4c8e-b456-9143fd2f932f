import json

import pandas as pd
import requests
from bs4 import BeautifulSoup

from tool.llm_tool import get_not_reason_llm_response_with_retry


def generate_detail_info():
    df = pd.read_excel('cruiseport_output_result.xlsx')
    target_fields = [
        '港口中文名', '港口英文名', '所在洲', '所在国家', '所在省\州', '所在城市',
        '详细所在地', '经度', '纬度', '港口地理信息', '港口类型（按功能功能划分）',
        '港口类型（按地理位能划分）', '港口类型（按潮汐影响划分）', '港口类型（按是否结冻分类）','港口规模'
    ]
    # 初始化新列（避免KeyError）
    for field in target_fields:
        if field not in df.columns:
            df[field] = None
    #遍历df
    for index, row in df.iterrows():
        airport_info = row.to_dict()
        prompt = f'''
请根据给出的港口信息，以及大模型自带的信息，按照严格的JSON形式返回该港口的下列字段信息。
字段要求：
1、包含所在洲、所在国家、所在省\州、所在城市、详细所在地、经度、纬度、港口类型（按功能功能划分）、港口类型（按地理位能划分）、港口类型（按潮汐影响划分）、港口类型（按是否结冻分类）、港口规模
2、各字段要求
-所在洲：港口所在的大洲名称
-所在国家：港口所在的国家名称
-所在省\州：港口所在的国家内的省或州名称
-所在城市：港口所在的城市名称
-详细所在地：港口所在洲、所在国家、所在省\州、所在城市字段通过-拼接而来
-经度：港口所在地的经度，为浮点类型
-纬度：港口所在地的纬度，为浮点类型
-港口类型（按功能功能划分）：取值范围[商港, 渔港, 工业港, 军港, 避风港, 旅游港]
  --取值说明：
    商港‌：供商船往来停靠，办理客货运输业务，如青岛港、上海港等‌；
    渔港‌：专供渔船停泊、卸下渔获物并进行补给修理，如江苏吕泅渔港、宁波象山石埔渔港等‌；
    工业港‌：为某一工业企业服务，负责原料、产品及所需物资的装卸转运，如广东湛江港等‌；
    军港‌：专供军用舰船使用，如旅顺港、威海卫港；
    避风港‌：供船舶躲避风浪，通常设施简单，如太平洋上的三大群岛等‌；
    旅游港‌：专供旅游船只停泊，提供旅游服务‌
-港口类型（按地理位能划分）：取值范围[海港, 河港, 河口港, 湖港, 水库港]
  --取值说明：
    海港：位于海岸线附近，直接面向大海的港口；
    河港：位于河流入海口，直接面向河流的港口；
    河口港：位于两条河流交汇处，直接面向两条河流的港口；
    湖港：位于湖泊边，直接面向湖泊的港口；
    水库港：位于水库边，直接面向水库的港口；
-港口类型（按潮汐影响划分）：取值范围[开敞港, 闭合港, 混合港]
  --取值说明：
    开敞港：完全受潮汐影响的港口，如日本北海道地区的港口；
    闭合港：完全不受潮汐影响的港口，如荷兰的阿姆斯特丹港；
    混合港：受潮汐影响和不受潮汐影响的港口，如中国的上海港；
-港口类型（按是否结冻分类）：取值范围[冻港, 不冻港]
  --取值说明：
    冻港：受极寒气候影响，冬季港口结冰，船舶无法通航的港口；
    不冻港：不受极寒气候影响，全年均可通航的港口；
-港口规模：取值范围[特大型港口, 大型港口, 中型港口, 小型港口]
  --取值说明：
    特大型港口：年吞吐量超过1000万吨的港口；
    大型港口：年吞吐量在500-1000万吨之间的港口；
    中型港口：年吞吐量在100-500万吨之间的港口；
    小型港口：年吞吐量在100万吨以下的港口；
3、如果给出的港口信息中没有以上字段需要的内容，请根据自身大模型知识进行判断。  


输出要求：
1、以严格的JSON格式输出以上的港口信息，JSON字段名严格和上面的字段名保持一致。
2、直接输出最终的JSON数据，不需要任何解释和说明，也不需要```json等标记。
3、如果港口场信息中存在缺失字段，请用“无”填充

给出的港口信息:
        '''
        #将json数据写入prompt
        prompt += str(airport_info)
        result = get_not_reason_llm_response_with_retry(prompt)
        print(result)
        try:
            # 解析JSON结果
            json_data = json.loads(result)

            # 更新数据到当前行
            for key in json_data.keys():
                df.at[index, key] = json_data.get(key, '无')

        except (json.JSONDecodeError, AttributeError) as e:
            print(f"行 {index} 解析失败: {str(e)}")
        # 保存结构化结果到新Excel
    df.to_excel('cruiseport_output_result.xlsx',
                index=False,
                engine='openpyxl')  # 确保支持xlsx格式



if __name__ == '__main__':
    generate_detail_info()

