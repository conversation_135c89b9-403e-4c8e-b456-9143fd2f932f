import os
from sqlite3 import connect

import pandas as pd
from sqlalchemy import create_engine, Boolean, Integer, Text, Float, DateTime, Column, Table, MetaData

from tool.file_tool import FileUtils
from tool.llm_tool import llm_not_reason, get_not_reason_llm_response_with_retry


class ExcelToSqlite:

    @staticmethod
    def _validate_columns(df, columns):
        """验证列是否存在"""
        missing = [col for col in columns if col not in df.columns]
        if missing:
            raise ValueError(f"以下列不存在于Excel中：{', '.join(missing)}")

    @staticmethod
    def excel_to_sqlite(
            excel_path: str,
            db_path: str,
            table_name: str = 'excel_data',
            dtype_mapping: dict = None,
            primary_keys: list = None
    ) -> None:
        """
        将Excel数据迁移到SQLite数据库（支持主键和类型定义）

        :param excel_path: Excel文件路径
        :param db_path: SQLite数据库路径
        :param table_name: 目标表名（默认'excel_data'）
        :param dtype_mapping: 列类型映射字典，如 {'列名': Integer}
        :param primary_keys: 主键列名列表，如 ['id']
        """
        try:
            # 读取并预处理数据
            df = pd.read_excel(excel_path)
            df.columns = [col.replace(' ', '_').replace('(', '').replace(')', '')
                          for col in df.columns]

            # 校验主键列
            if primary_keys:
                ExcelToSqlite._validate_columns(df, primary_keys)

            # 创建数据库引擎
            engine = create_engine(f'sqlite:///{db_path}')
            metadata = MetaData()

            # 构建列定义
            columns = []
            dtype_mapping = dtype_mapping or {}
            for col in df.columns:
                column_info = dtype_mapping.get(col)
                if type(column_info) is list:
                    col_type = column_info[0]
                    col_comment = column_info[1]
                    is_pk = primary_keys and (col in primary_keys)
                    columns.append(Column(col, col_type, comment=col_comment, primary_key=is_pk))
                else:
                    col_type = dtype_mapping.get(col, Text)
                    is_pk = primary_keys and (col in primary_keys)
                    columns.append(Column(col, col_type, primary_key=is_pk))

            # 显式创建表（支持复合主键）
            Table(
                table_name,
                metadata,
                *columns,
                sqlite_autoincrement=True  # SQLite自增主键支持
            ).create(engine, checkfirst=True)

            # 追加数据
            df.to_sql(
                name=table_name,
                con=engine,
                if_exists='append',
                index=False,
                chunksize=1000
            )

            # 数据完整性验证
            with connect(db_path) as conn:
                db_count = pd.read_sql(f"SELECT COUNT(*) FROM {table_name}", conn).iloc[0, 0]
                print(f"迁移完成：Excel记录数 {len(df)} | 数据库记录数 {db_count}")

        except Exception as e:
            print(f"操作失败：{str(e)}")
            raise

    @staticmethod
    def save_airport_data():
        airport_dtype_mapping = {
            'UID': Integer,  # 整型
            'SCALE_RANK': Integer,  # 整型
            'WIKIPEDIA': Text,  # 文本型
            'NALTSCALE': Integer,  # 整型
            'WIKIDATAID': Text,  # 文本型
            'WDID_SCORE': Integer,  # 整型
            'DETAIL_INFO': Text,  # 文本型
            'NAME_CH': Text,  # 文本型
            'NAME_EN': Text,  # 文本型
            'CONTINENT': Text,  # 文本型
            'COUNTRY': Text,  # 文本型
            'PROVINCE_STATE': Text,  # 文本型
            'CITY': Text,  # 文本型
            'LONGITUDE': Float,  # 浮点型
            'LATITUDE': Float,  # 浮点型
            'LOCATION_DESCRIBE': Text,
            'ALTITUDE': Float,  # 浮点型
            'AIRPORT_TYPE': Text,  # 文本型
            'IATA_CODE': Text,  # 文本型
            'ICAO_CODE': Text,  # 文本型
            'AIRPORT_SCALE': Text,  # 文本型
            'OPERATION_INFO': Text
        }
        ExcelToSqlite.excel_to_sqlite(
            excel_path='airport_database_input.xlsx',
            db_path=FileUtils.get_database_file_path(),
            table_name='tb_airport',
            primary_keys=['UID'],  # 指定主键列
            dtype_mapping=airport_dtype_mapping
        )

    @staticmethod
    def save_cruiseport_data():
        cruiseport_dtype_mapping = {
            'UID': Integer,  # 整型
            'NAME_CH': Text,  # 文本型
            'NAME_EN': Text,  # 文本型
            'LOCATION_DESCRIBE': Text,
            'FACILITIES': Text,
            'PORT_CODE': Text,
            'INTRODUCTION': Text,
            'CONTINENT': Text,  # 文本型
            'COUNTRY': Text,  # 文本型
            'PROVINCE_STATE': Text,  # 文本型
            'CITY': Text,  # 文本型
            'LONGITUDE': Float,  # 浮点型
            'LATITUDE': Float,  # 浮点型
            'FUNCTION_TYPE': Text,
            'LOCATION_TYPE': Text,  # 文本型
            'TIDAL_INFLUENCE_TYPE': Text,  # 文本型
            'FREEZING_TYPE': Text,  # 文本型
            'SCALE': Text,  # 文本型
        }
        ExcelToSqlite.excel_to_sqlite(
            excel_path='cruiseport_database_input.xlsx',
            db_path=FileUtils.get_database_file_path(),
            table_name='tb_cruiseport',
            primary_keys=['UID'],  # 指定主键列
            dtype_mapping=cruiseport_dtype_mapping
        )

    @staticmethod
    def save_hydro_data():
        hydro_dtype_mapping = {
            'ID': [Text, '唯一标识符'],
            'country': [Text, '所在国家'],
            'name_ch': [Text, '水电站中文名称'],
            'name_en': [Text, '水电站英文名称'],
            'capacity_mw': [Float, '水电站装机容量，单位：兆瓦 (MW)'],
            'plant_lat': [Float, '水电站纬度'],
            'plant_lon': [Float, '水电站经度'],
            'plant_type': [Text, '水电站类型，取值范围[径流式, 蓄水式, 抽水蓄能式, 运河式]'],
            'year': [Integer, '投运年份'],
            'dam_name': [Text, '大坝中文名称'],
            'dam_height_m': [Float, '大坝高度，单位：米 (m)'],
            'man_dam_lat': [Float, '手动采集的大坝纬度'],
            'man_dam_lon': [Float, '手动采集的大坝经度'],
            'river': [Text, '所在河流中文名称'],
            'head_m': [Float, '水头，指水电站实际可利用的水位差（单位：米）'],
            'res_avg_depth_m': [Float, '水库平均深度，单位：米 (m)'],
            'res_area_km2': [Float, '水库面积，单位：平方公里 (km²)'],
            'res_vol_km3': [Float, '水库容积，单位：立方公里 (km³)'],
            'continent': [Text, '所在大洲，取值范围[亚洲, 欧洲, 北美洲, 南美洲, 大洋洲, 非洲]'],
            'province_state': [Text, '所在省份或州或邦，简体中文名称，如陕西省, 德克萨斯州, 北方邦等。字段作用：用于根据所在省、州、邦等名称过滤数据'],
            'city': [Text, '所在城市名称'],
        }
        ExcelToSqlite.excel_to_sqlite(
            excel_path='GloHydroRes_database_input.xlsx',
            db_path=FileUtils.get_database_file_path(),
            table_name='tb_hydro_data',
            primary_keys=['ID'],  # 指定主键列
            dtype_mapping=hydro_dtype_mapping
        )

    @staticmethod
    def generate_db_create_sql_from_describe(describe):
        prompt = f'''
你是一位精通SQLite数据库的专家，请根据给出的结构化数据描述信息，生成对应数据库表的create语句，要求在每个字段后面按格式添加语义描述信息。

##数据描述信息##：
{describe}

##输出要求##:
1、输出的create sql语句，必须符合SQLite数据库的语法规范。
2、输出的create sql语句，必须包含所有字段，并且字段类型、描述信息必须与数据描述信息中一致。
3、输出的create sql语句，必须包含主键字段，并且主键字段必须自增。
4、只需要输出最终create sql语句，无需输出其他解释说明的内容。

##输出示例##：
CREATE TABLE tb_airport (
    "UID" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,    -- 主键自增ID
    "NAME_CH" TEXT,                                      -- 机场中文名称 
    "NAME_EN" TEXT,                                      -- 机场英文名称
    "CONTINENT" TEXT,                                    -- 机场所在大洲，取值范围[亚洲, 欧洲, 北美洲, 南美洲, 大洋洲, 非洲]。字段作用：用于根据洲名称来过滤数据
    "COUNTRY" TEXT,                                      -- 机场所在国家，简体中文名称，如中国, 美国, 英国等。字段作用：用于根据国家名称来过滤数据
    "LONGITUDE" FLOAT,                                   -- 机场所在经度
    "LATITUDE" FLOAT,                                    -- 机场所在纬度
    "LOCATION_DESCRIBE" TEXT,                            -- 机场所在位置描述，如中国陕西省西安市临潼区
    "ALTITUDE" FLOAT,                                    -- 机场海拔高度，单位为米
)
        '''
        create_sql = get_not_reason_llm_response_with_retry(prompt)
        print(create_sql)

if __name__ == '__main__':
    #ExcelToSqlite.save_airport_data()
    #ExcelToSqlite.save_cruiseport_data()
    # ExcelToSqlite.save_hydro_data()
    ExcelToSqlite.generate_db_create_sql_from_describe('''CREATE TABLE tb_cruiseport (
            "UID" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,       -- 主键自增ID
            "NAME_CH" TEXT,                                         -- 港口中文名称
            "NAME_EN" TEXT,                                         -- 港口英文名称
            "LOCATION_DESCRIBE" TEXT,                               -- 港口所在位置描述
            "FACILITIES" TEXT,                                      -- 港口设施 
            "PORT_CODE" TEXT,                                       -- 港口代码
            "INTRODUCTION" TEXT,                                    -- 港口介绍
            "CONTINENT" TEXT,                                       -- 港口所在大洲，取值范围[亚洲, 欧洲, 北美洲, 南美洲, 大洋洲, 非洲, 南极洲]。字段作用：用于根据洲名称来过滤数据
            "COUNTRY" TEXT,                                         -- 港口所在国家，简体中文名称，如中国, 美国, 英国等。字段作用：用于根据国家名称来过滤数据
            "PROVINCE_STATE" TEXT,                                   -- 港口所在省份或州或邦，简体中文名称，如陕西省, 德克萨斯州, 北方邦等。字段作用：用于根据所在省、州、邦等名称过滤数据
            "CITY" TEXT,                                             -- 港口所在城市，简体中文名称，如西安市, 达拉斯, 莫斯科等。字段作用：用于根据城市名过滤数据
            "LONGITUDE" FLOAT,                                       -- 港口所在经度
            "LATITUDE" FLOAT,                                        -- 港口所在纬度
            "FUNCTION_TYPE" TEXT,                                     -- 港口功能类型，取值范围[商港, 渔港, 工业港, 军港, 避风港, 旅游港]
            "LOCATION_TYPE" TEXT,                                    -- 港口位置类型，取值范围[海港, 河港, 河口港, 湖港, 水库港]
            "TIDAL_INFLUENCE_TYPE" TEXT,                             -- 港口受潮汐影响类型，取值范围[开敞港, 闭合港, 混合港]
            "FREEZING_TYPE" TEXT,                                    -- 港口是否冻结类型，取值范围[冻港, 不冻港]
            "SCALE" TEXT                                            -- 港口规模，取值范围[特大型港口, 大型港口, 中型港口, 小型港口]
        )''')