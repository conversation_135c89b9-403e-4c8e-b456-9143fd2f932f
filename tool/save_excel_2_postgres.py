
from sqlalchemy import create_engine, text
import pandas as pd

from template_config.constants_config import POSTGRESQL_USER, POSTGRESQL_PASSWORD, POSTGRESQL_HOST, POSTGRESQL_PORT, \
    POSTGRESQL_DB_NAME
from tool.llm_tool import get_not_reason_llm_response_with_retry

def ask_llm_for_pg_schema(description_text: str, table_name: str) -> str:
    prompt = f"""
你是一个资深数据库工程师，请根据以下数据说明，生成一个 PostgreSQL 的建表 SQL 语句，包括：
1. `CREATE TYPE` 枚举定义（当字段的取值范围有限时）
2. `CREATE TABLE` 语句（字段名、字段类型、主键）
3. 表注释（COMMENT ON TABLE）
4. 每个字段的中文注释（COMMENT ON COLUMN）

要求：
- 字段类型要符合 PostgreSQL 语法
- 如果字段说明中提到了“取值范围”或列举了若干个可能值，请使用 PostgreSQL 的 ENUM 类型定义
- 主键字段设为 uid BIGSERIAL PRIMARY KEY（如果有提到主键）
- 根据字段说明内容选择合适的字段类型，经纬度相关的字段使用DOUBLE PRECISION类型
- 请使用标准 SQL 输出，字段说明要加注释

数据说明如下：
{description_text}

表名为：{table_name}
"""
    response = get_not_reason_llm_response_with_retry(prompt)
    print(response)
    return response


def create_table_and_insert(excel_path, sheet_name, db_url, table_name, schema_sql):
    engine = create_engine(db_url)
    with engine.connect() as conn:
        print("执行建表 SQL：\n")
        print(schema_sql)
        conn.execute(text(schema_sql))

        # 读取 Excel 并写入数据库（跳过 uid）
        df = pd.read_excel(excel_path, sheet_name=sheet_name)
        if 'uid' in df.columns:
            df = df.drop(columns=['uid'])
        df.to_sql(table_name, engine, if_exists='append', index=False)
        print("✅ 数据已插入数据库")


# ================================
# 主程序入口
# ================================
if __name__ == "__main__":
    # 📝 你的自然语言数据说明（可从txt、docx读取）
    data_description = """
该表用于记录全球邮轮港口信息，每条记录包含港口的中英文名称、地理位置描述、设施信息、国际代码、详细介绍、所属洲、国家、省、市，以及经纬度。此外，还有港口的功能类型、位置类型、受潮汐影响类型、是否冻结情况和港口规模等信息。主键为uid，自增。
"""

    table_name = "tb_cruiseport"
    schema_sql = ask_llm_for_pg_schema(data_description, table_name)

    db_url = f"postgresql+psycopg2://{POSTGRESQL_USER}:{POSTGRESQL_PASSWORD}@{POSTGRESQL_HOST}:{POSTGRESQL_PORT}/{POSTGRESQL_DB_NAME}"
    excel_path = "ports.xlsx"
    sheet_name = "Sheet1"

    create_table_and_insert(excel_path, sheet_name, db_url, table_name, schema_sql)