import os


class  FileUtils(object):

    @staticmethod
    def get_project_path():
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        return project_root

    @staticmethod
    def get_vector_dir():
        project_root = FileUtils.get_project_path()
        vector_dir = os.path.join(project_root, "data", "knowledge_data")
        return vector_dir

    @staticmethod
    def get_database_dir():
        project_root = FileUtils.get_project_path()
        database_dir = os.path.join(project_root, "data", "database")
        return database_dir

    @staticmethod
    def get_database_file_path():
        project_root = FileUtils.get_project_path()
        database_dir = os.path.join(project_root, "data", "database", "database.db")
        return database_dir