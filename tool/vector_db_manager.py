import json
import os
import faiss
import numpy as np
import pandas as pd
from model.azure_client import AzureClient
from tool.file_tool import FileUtils


class VectorDBManager:

    def __init__(self, index_name, input_file_name, parquet_file_name, id_column_name, knowledge_dir_name, data_type='text'):
        self.VECTOR_DIR = FileUtils.get_vector_dir()
        self.MAX_TOKENS = 8191  # text-embedding-3-large 支持的最大token数
        self.BATCH_SIZE = 10  # 新增批量处理大小
        self.text_columns = []
        self.input_file_path = os.path.join(self.VECTOR_DIR, knowledge_dir_name, input_file_name)
        self.parquet_file_path = os.path.join(self.VECTOR_DIR, knowledge_dir_name, parquet_file_name)
        # 索引更新优化
        self.index_path = os.path.join(self.VECTOR_DIR, knowledge_dir_name, index_name)
        self.id_column_name = id_column_name
        self.data_type = data_type

    def combine_row(self, row):
        if self.data_type == 'text':
            parts = []
            for col in self.text_columns:
                value = str(row[col]) if not pd.isna(row[col]) else ''
                value = value[:2000]
                parts.append(f"{col}:{value}")
            combined = '\n'.join(parts)
            return combined
        elif self.data_type == 'json':
            combined = {}
            for col in self.text_columns:
                value = str(row[col]) if not pd.isna(row[col]) else ''
                value = value[:2000]
                combined[col] = value
            return json.dumps(combined, ensure_ascii=False)

    def get_input_file_data(self):
        #分别处理csv和excel文件
        if self.input_file_path.endswith('.csv'):
            return pd.read_csv(self.input_file_path)
        elif self.input_file_path.endswith('.xlsx'):
            return pd.read_excel(self.input_file_path)
        else:
            raise ValueError("不支持的文件格式")

    def save_input_file_data(self):
        # 原子写入临时文件
        tmp_parquet = os.path.join(self.VECTOR_DIR, "tmp.parquet")
        tmp_index = os.path.join(self.VECTOR_DIR, "tmp.index")

        try:
            # 原有读取逻辑...
            existing_ids = set()
            existing_df = pd.DataFrame()
            if os.path.exists(self.parquet_file_path):
                existing_df = pd.read_parquet(self.parquet_file_path)
                existing_ids = set(existing_df[self.id_column_name])

            new_df = self.get_input_file_data()
            self.text_columns = new_df.columns.tolist()

            # 批量处理优化
            embeddings = []
            current_batch = []
            valid_rows = []

            for idx, row in new_df.iterrows():
                print(f"Processing row {idx+1}/{new_df.shape[0]}...")
                if row[self.id_column_name] in existing_ids:
                    print(f"跳过已存在的数据: {row[self.id_column_name]}")
                    continue
                row['combined_text'] = self.combine_row(row)
                current_batch.append(row['combined_text'])
                valid_rows.append(row)

                # 批量处理
                if len(current_batch) >= self.BATCH_SIZE:
                    for attempt in range(3):  # 简单重试机制
                        try:
                            print(f"处理批次: {len(current_batch)}")
                            emb = AzureClient.get_embeddings(current_batch)
                            embeddings.extend(emb)
                            current_batch = []
                            break
                        except Exception as e:
                            print(f"重试中... 错误: {e}")
                            if attempt == 2: raise

            # 处理剩余批次
            if current_batch:
                emb = AzureClient.get_embeddings(current_batch)
                embeddings.extend(emb)

            if not embeddings:
                print("没有新的嵌入向量需要添加")
                return

            if os.path.exists(self.index_path):
                print("索引文件已存在，追加更新...")
                index = faiss.read_index(self.index_path)
                index.add(np.array(embeddings).astype('float32'))  # 直接追加
                print("索引更新完成")
            else:
                print("创建索引...")
                index = faiss.IndexFlatIP(len(embeddings[0]))
                index.add(np.array(embeddings).astype('float32'))
                print("索引创建完成")
            # 写入
            print("写入中...")
            faiss.write_index(index, tmp_index)
            print("写入完成")
            print("元数据写入中...")
            pd.concat([existing_df, pd.DataFrame(valid_rows)]).to_parquet(tmp_parquet)
            print("元数据写入完成")
            # 替换旧文件
            os.replace(tmp_index, self.index_path)
            os.replace(tmp_parquet, self.parquet_file_path)

        finally:
            # 清理临时文件
            for fpath in [tmp_parquet, tmp_index]:
                if os.path.exists(fpath):
                    os.remove(fpath)


    @staticmethod
    def save_event_news_data():
        vector_db_manager = VectorDBManager("news_index.index", "news_output.csv",
                                            "news_metadata.parquet", '_id', 'news_knowledge')
        vector_db_manager.save_input_file_data()

    @staticmethod
    def save_icons_data():
        vector_db_manager = VectorDBManager("icons_index.index", "map_symbol.xlsx",
                                            "icons_metadata.parquet", '序号', 'icons_knowledge',
                                            data_type='json')
        vector_db_manager.save_input_file_data()

    @staticmethod
    def save_airports_data():
        vector_db_manager = VectorDBManager("airports_index.index", "airport_output_result.xlsx",
                                            "airports_metadata.parquet", '序号', 'airport_knowledge',
                                            data_type='json')
        vector_db_manager.save_input_file_data()

    @staticmethod
    def save_cruiseports_data():
        vector_db_manager = VectorDBManager("cruiseports_index.index", "cruiseport_output_result.xlsx",
                                            "cruiseports_metadata.parquet", '序号', 'cruiseport_knowledge',
                                            data_type='json')
        vector_db_manager.save_input_file_data()

# 使用示例
if __name__ == "__main__":
    VectorDBManager.save_event_news_data()
    #VectorDBManager.save_icons_data()
    #VectorDBManager.save_airports_data()
    #VectorDBManager.save_cruiseports_data()
