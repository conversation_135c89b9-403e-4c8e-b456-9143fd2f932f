import json
import re

from model.llm_provider import LLMProvider

llm = LLMProvider().get_client(is_reason=True)
llm_not_reason = LLMProvider().get_client(is_reason=False)
llm_not_reason_speed_first = LLMProvider().get_client(is_reason=False, speed_first=True)

def clear_think_content(text):
    return re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL).strip()


def get_llm_response_with_retry(prompt, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = llm.invoke(prompt, timeout=180).content.strip()
            response = clear_think_content(response)
            return response
        except Exception as e:
            print(f"Retrying due to error: {e}")
            continue
    print("Max retries reached. Exiting.")
    return ''

def get_not_reason_llm_response_with_retry(prompt, max_retries=3):
    # 遍历最大重试次数
    for attempt in range(max_retries):
        try:
            # 调用llm_not_reason.invoke函数，传入prompt参数，设置超时时间为180秒，获取返回的response
            response = llm_not_reason.invoke(prompt, timeout=180).content.strip()
            # 调用clear_think_content函数，传入response参数，清除思考内容
            response = clear_think_content(response)
            # 返回response
            return response
        except Exception as e:
            # 打印错误信息
            print(f"Retrying due to error: {e}")
            # 继续下一次循环
            continue
    # 打印最大重试次数已达到的信息
    print("Max retries reached. Exiting.")
    # 返回空字符串
    return ''

def get_not_reason_llm_response_with_retry_speed_first(prompt, max_retries=3):
    # 遍历最大重试次数
    for attempt in range(max_retries):
        try:
            # 调用llm_not_reason.invoke函数，传入prompt参数，设置超时时间为180秒，获取返回的response
            response = llm_not_reason_speed_first.invoke(prompt, timeout=180).content.strip()
            # 调用clear_think_content函数，传入response参数，清除思考内容
            response = clear_think_content(response)
            # 返回response
            return response
        except Exception as e:
            # 打印错误信息
            print(f"Retrying due to error: {e}")
            # 继续下一次循环
            continue
    # 打印最大重试次数已达到的信息
    print("Max retries reached. Exiting.")
    # 返回空字符串
    return ''


def parse_json_result_2_dict(json_result):
    if type(json_result) == str:
        #如果json_result以```开头
        if json_result.startswith('```'):
            #分别获取第一个{和最后一个}的下标，然后通过下标获取json内容
            start_index = json_result.index('{')
            end_index = json_result.rindex('}')
            json_result = json_result[start_index:end_index + 1]
        return json.loads(json_result)
    else:
        return json_result
