import sqlite3
from typing import Optional, List, Dict, Any
from threading import Lock
from tool.file_tool import FileUtils

class SQLiteDB:
    """SQLite数据库工具类，提供连接复用机制和完整的CRUD功能"""
    
    _instance_lock = Lock()
    _connections = {}
    
    def __new__(cls):
        """单例模式实现，确保全局唯一连接池"""
        if not hasattr(cls, '_instance'):
            with cls._instance_lock:
                if not hasattr(cls, '_instance'):
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化连接池配置"""
        self.db_path = FileUtils.get_database_file_path()
        self._pool_lock = Lock()
        
    def _get_connection(self) -> sqlite3.Connection:
        """获取数据库连接（线程安全）
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        import threading
        thread_id = threading.get_ident()
        
        with self._pool_lock:
            if thread_id not in self._connections:
                self._connections[thread_id] = sqlite3.connect(
                    self.db_path, 
                    check_same_thread=False,
                    isolation_level=None
                )
                self._connections[thread_id].row_factory = sqlite3.Row
            return self._connections[thread_id]
    
    def execute_query(self, query: str, params: tuple = None) -> sqlite3.Cursor:
        """
        执行SQL查询（自动管理事务）
        
        Args:
            query: SQL语句
            params: 参数元组
        
        Returns:
            游标对象
        """
        conn = self._get_connection()
        try:
            with conn:
                if params:
                    cursor = conn.execute(query, params)
                else:
                    cursor = conn.execute(query)
                conn.commit()
                return cursor
        except sqlite3.Error as e:
            print(f"数据库错误: {e}")
            conn.rollback()
            raise
    
    def create_table(self, table_name: str, columns: Dict[str, str]) -> None:
        """
        创建数据表（如果不存在）
        
        Args:
            table_name: 表名
            columns: 列定义字典 {列名: 类型}
        """
        columns_with_types = [f"{name} {type_}" for name, type_ in columns.items()]
        query = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns_with_types)})"
        self.execute_query(query)
    
    def insert(self, table_name: str, data: Dict[str, Any]) -> int:
        """
        插入单条数据
        
        Args:
            table_name: 表名
            data: 数据字典 {列名: 值}
        
        Returns:
            int: 最后插入行ID
        """
        keys = ', '.join(data.keys())
        placeholders = ', '.join(['?'] * len(data))
        values = tuple(data.values())
        query = f"INSERT INTO {table_name} ({keys}) VALUES ({placeholders})"
        cursor = self.execute_query(query, values)
        return cursor.lastrowid if cursor else -1
    
    def update(self, table_name: str, data: Dict[str, Any], where_clause: str, params: tuple) -> int:
        """
        更新数据记录
        
        Args:
            table_name: 表名
            data: 更新数据字典
            where_clause: WHERE条件语句
            params: 条件参数元组
        
        Returns:
            int: 受影响行数
        """
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        values = tuple(data.values()) + params
        query = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"
        cursor = self.execute_query(query, values)
        return cursor.rowcount if cursor else 0
    
    def delete(self, table_name: str, where_clause: str, params: tuple) -> int:
        """
        删除数据记录
        
        Args:
            table_name: 表名
            where_clause: WHERE条件语句
            params: 条件参数元组
        
        Returns:
            int: 受影响行数
        """
        query = f"DELETE FROM {table_name} WHERE {where_clause}"
        cursor = self.execute_query(query, params)
        return cursor.rowcount if cursor else 0
    
    def fetch_one(self, query: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """
        获取单条记录
        
        Args:
            query: SQL查询语句
            params: 参数元组
        
        Returns:
            Optional[Dict]: 单条记录字典或None
        """
        cursor = self.execute_query(query, params)
        if cursor:
            row = cursor.fetchone()
            if row:
                return dict(zip([desc[0] for desc in cursor.description], row))
        return None
    
    def fetch_all(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """
        获取所有记录
        
        Args:
            query: SQL查询语句
            params: 参数元组
        
        Returns:
            List[Dict]: 记录字典列表
        """
        cursor = self.execute_query(query, params)
        if cursor:
            return [dict(zip([desc[0] for desc in cursor.description], row)) 
                   for row in cursor.fetchall()]
        return []
    
    def query(self, sql: str) -> List[Dict[str, Any]]:
        """
        简便查询方法
        
        Args:
            sql: 完整SQL语句
        
        Returns:
            List[Dict]: 查询结果字典列表
        """
        return self.fetch_all(query=sql)
    
    def close_all(self) -> None:
        """关闭所有连接池中的连接"""
        with self._pool_lock:
            for conn in self._connections.values():
                try:
                    conn.close()
                except Exception:
                    pass
            self._connections.clear()

    @staticmethod
    def query_with_sql(sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        db = SQLiteDB()

        # 多次查询复用同一连接
        result1 = db.query(sql)
        db.close_all()
        return result1

if __name__ == '__main__':
    SQLiteDB.query_with_sql("SELECT * FROM tb_airport WHERE country like '日本%'")