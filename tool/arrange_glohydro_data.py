import json

import pandas as pd
import requests
from bs4 import BeautifulSoup

from tool.llm_tool import get_not_reason_llm_response_with_retry


def generate_detail_info():
    df = pd.read_excel('GloHydroRes_vs1_output.xlsx')

    #遍历df
    for index, row in df.iterrows():
        hydro_info = row.to_dict()

        #判断continent是否为nan
        if pd.notna(hydro_info['continent']) and str(hydro_info['continent']).strip() != '':
            continue
        print(f'正在处理第{index}条数据')
        prompt = f'''
你是一位专业的全球水电站信息整理专家，请根据我发你的信息和大模型自己具备的知识，按下面要求对该水电站信息进行输出。        
        
要求：
1、输出该水电站的continent、country、name、plant_type、dam_name、res_name、river、province_state、city字段信息
2、continent为水电站所在大洲的中文名称、province_state为水电站所在国家内的省或州中文名称、city为水电站所在城市的中文名称，这几个字段根据大模型知识填成
2、country、name、plant_type、dam_name、res_name、river字段根据给出的现有内容翻译或转换成简体中文
2、country、name、plant_type、dam_name、res_name、river字段对应的中文含义分别是
  country：所在国家
  name：水电站名称
  plant_type：电站类型，中英转换规则：{{"STO": "蓄水式", "ROR": "径流式", "PS": "抽水蓄能式","Canal": "运河式", "NA": "无"}}
  dam_name：大坝名称
  res_name：水库名称
  river：所在河流
3、输出的中文名称为官方经常使用的字样


输出要求：
1、以上的每个字段必须转换成简体中文！！！
1、以严格的JSON格式输出以上的水电站信息，JSON字段名严格和上面的字段名保持一致。
2、直接输出最终的JSON数据，不需要任何解释和说明，也不需要```json等标记。
3、如果对应字段值为空，这输出的json中该字段也必须为空。

输出示例：
{{
"continent": "亚洲",
"country": "中国",
"name": "龙宫水电站",
"plant_type": "蓄水式",
"dam_name": "龙宫大坝",
"res_name": "龙宫水库",
"river": "长江",
"province_state": "四川省",
"city": "宜宾市"
}}


给出的水电站信息:
        '''
        #将json数据写入prompt
        prompt += str(hydro_info)
        result = get_not_reason_llm_response_with_retry(prompt)
        print(result)
        try:
            # 解析JSON结果
            json_data = json.loads(result)

            # 更新数据到当前行
            for key, value in json_data.items():
                df.at[index, key] = value

        except (json.JSONDecodeError, AttributeError) as e:
            print(f"行 {index} 解析失败: {str(e)}")
        # 保存结构化结果到新Excel
    df.to_excel('GloHydroRes_vs1_output.xlsx',
                index=False,
                engine='openpyxl')  # 确保支持xlsx格式



if __name__ == '__main__':
    generate_detail_info()


