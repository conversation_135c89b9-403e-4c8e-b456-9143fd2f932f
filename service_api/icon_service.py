import os.path
import json
from service_api.knowledge_service import KnowledgeService
from template_config import constants_config
from template_config.constants_config import PARSE_RESULT_KEY_OF_DRAW_CONTENT, ICON_KNOWLEDGE_NAME, \
    VECTOR_INDEX_PATH_NAME, VECTOR_METADATA_PATH_NAME, DRAW_DATA_ICON_FIELD_NAME
from template_config.knowledge_config import ICON_KNOWLEDGE
from tool.llm_tool import get_not_reason_llm_response_with_retry
from tool.vector_db_search import VectorSearch


class IconService:
    def __init__(self):
        pass
    @staticmethod
    def search_icon(search_str):
        knowledge_info = ICON_KNOWLEDGE[ICON_KNOWLEDGE_NAME]
        index_path = KnowledgeService.get_knowledge_path(knowledge_info, VECTOR_INDEX_PATH_NAME)
        meta_file_path = KnowledgeService.get_knowledge_path(knowledge_info, VECTOR_METADATA_PATH_NAME)
        meta_data, search_result = VectorSearch(index_path, meta_file_path).filtered_search(search_str, None, 5)
        return meta_data, search_result

    @staticmethod
    def get_icon_search_str(draw_content):
        default_query = (f'请选出最适合{draw_content}场景的地图标绘图标。')
        prompt = (f'''
请根据标绘内容，返回最适合该场景的图标名称。
##标绘内容##：
{draw_content}

##可选图标名称##：
[机场, 港口, 航母, 水电站, 大坝, 地震, 其他]

##输出要求##：
1、请严格从可选图标名称中选一个值输出。
2、只需要输出最终的图标名称，不需要任何其他解释或说明。
3、新闻事件属于其他

##输出示例1##：
港口

##输出示例2##：
机场

''')
        icon_result = get_not_reason_llm_response_with_retry(prompt)
        print(icon_result)
        icon_dict = {
            '机场': '红色圆圈内有一个飞机图案，代表机场或航空相关事物，常用于指示机场位置、航空服务标识等。红色代表强调或者警示。',
            '港口': '港口可抛锚停靠标志',
            '航母': '黄色，图标以黑色剪影呈现，展现出航空母舰的船体轮廓、舰岛，还有一架飞机在其上空飞行，体现出航空母舰作为海上飞机起降平台的关键特征。航空母舰是现代海军海上作战编队的核心。在军事行动中，它为舰载机提供起降和停放空间，可在远离本土的海域展开大规模空中作战，如夺取制空权、对敌方沿海目标实施空袭、为两栖登陆作战提供空中支援等。还能用于海上威慑，维护海洋权益 。',
            '水电站': '水电站，水能发电站场景，里面有水、电、闸等信息',
            '大坝': '大坝、水库场景。有水、坝等信息',
            '地震': '代表地震灾害，裂痕象征地震引发的地面开裂，会对建筑物造成破坏',
            '其他': default_query
        }
        if icon_result.strip() in icon_dict:
            query = icon_dict[icon_result]
        else:
            query = default_query
        return query

    @staticmethod
    def get_icon_results(draw_content):
        query = IconService.get_icon_search_str(draw_content)
        meta_data, search_result = IconService.search_icon(query)
        # print(meta_data)
        # print(search_result)
        final_result_json = json.loads(search_result[0])
        icon_path = IconService.get_icon_path_from_result_json(final_result_json)
        return icon_path

    @staticmethod
    def get_icon_path_from_semantic_task_info(icon_related_info):
        icon_path = IconService.get_icon_results(icon_related_info)
        return icon_path


    @staticmethod
    def get_icon_path_from_result_json(result_json):
        icon_file_name = result_json['文件命名']
        type = result_json['类别']
        #return os.path.join('svg_image', type, icon_file_name)
        # 关键修改点：使用正斜杠手动拼接路径（避免反斜杠转义问题）
        return f'svg_image/{type}/{icon_file_name}'

    @staticmethod
    def add_icon_info_for_draw_data(user_input, parse_result, draw_data):
        draw_content = parse_result[PARSE_RESULT_KEY_OF_DRAW_CONTENT]
        icon_path = IconService.get_icon_results(draw_content)
        if constants_config.PARSE_RESULT_KEY_OF_ICON_PATH in parse_result:
            parse_result[constants_config.PARSE_RESULT_KEY_OF_ICON_PATH] = icon_path
        if type(draw_data) == str:
            draw_data = json.loads(draw_data)
        for draw_item in draw_data:
            draw_item[DRAW_DATA_ICON_FIELD_NAME] = icon_path
        return json.dumps(draw_data)



if __name__ == '__main__':
    draw_data = '''
    [
{
    "time": "2025-04-28",
    "location": "也门，红海",
    "coord": [15.0, 42.5],
    "content": "胡塞武装导弹袭击杜鲁门航母"
},
{
    "time": "2025-04-28",
    "location": "也门，红海",
    "coord": [15.0, 42.5],
    "content": "杜鲁门号舰载机坠海"
}
]
    '''
    IconService.add_icon_info_for_draw_data('',{'标绘内容': '杜鲁门航母动态'}, draw_data)
    print(draw_data)
