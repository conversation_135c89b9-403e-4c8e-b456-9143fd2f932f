import os

from template_config.constants_config import VECTOR_INDEX_PATH_NAME, VECTOR_METADATA_PATH_NAME
from template_config.knowledge_config import PROJECT_KNOWLEDGE, K<PERSON><PERSON>LEDGE_DESCRIBE_KEY, <PERSON><PERSON><PERSON><PERSON><PERSON>GE_SCENES_KEY
from tool.file_tool import FileUtils


class KnowledgeService:
    def __init__(self):
        pass
    @staticmethod
    def get_knowledge_info_str():
        knowledge_info_str = ''
        for kn_type, kn_detail in PROJECT_KNOWLEDGE.items():
            knowledge_info_str += '知识库名称：' + kn_type + '\n'
            for key, value in kn_detail.items():
                if key in [KNOWLEDGE_DESCRIBE_KEY, KNOWLEDGE_SCENES_KEY]:
                    knowledge_info_str += '  -' + key + '：' + str(value) + '\n'
        return knowledge_info_str

    @staticmethod
    def parse_parse_knowledge_result(result, default_result):
        if result not in PROJECT_KNOWLEDGE:
            return default_result
        return result

    @staticmethod
    def get_knowledge_path(knowledge_info, file_type):
        vector_path = FileUtils.get_vector_dir()
        file_path = knowledge_info[VECTOR_INDEX_PATH_NAME] if file_type == VECTOR_INDEX_PATH_NAME else knowledge_info[VECTOR_METADATA_PATH_NAME]
        return os.path.join(vector_path, file_path)