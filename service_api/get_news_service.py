import re
import time
from datetime import datetime, timedelta

import requests
import json
from dateutil.parser import parse
from bs4 import BeautifulSoup

from get_global_events import extract_timeline_info, parse_llm_result
from tool.llm_tool import get_llm_response_with_retry


def get_cctv_world_news(api_url):

    # 关键请求头
    headers = {
        "Referer": "https://news.cctv.com/news/world/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }

    response = requests.get(api_url, headers=headers)
    response.encoding = 'utf-8'  # 显式设置编码
    raw_data = json.loads(response.text.strip("world(").rstrip(")"))
    time.sleep(3)
    # 解析数据结构
    news_list = []
    for item in raw_data["data"]["list"]:
        news_list.append({
            "title": item["title"],
            "url": item["url"],
            "pubtime": item["focus_date"],
            "image": item["image"],
            "content": item["brief"]
        })
    return news_list

def get_weekly_recent_news(task_logs):
    current_time = datetime.now()
    seven_days_ago = current_time - timedelta(days=7)
    # 根据当前时间，通过 news['pubtime'] 字段筛出最近7天内的数据， news['pubtime']数据格式为：'2025-04-12 21:44:47'，
    filtered_news = []
    for i in range(1, 4):
        # 核心接口（需动态生成页码）
        api_url = f"https://news.cctv.com/2019/07/gaiban/cmsdatainterface/page/world_{i}.jsonp?cb=world"
        task_logs.append(f"正在获取央视国际新闻第{i}页数据，具体请求地址为：{api_url}")
        news_list = get_cctv_world_news(api_url)
        #获取news_list中的title列表
        title_list = [news['title'] for news in news_list]
        task_logs.append(f"央视国际新闻第{i}页数据获取完成，获取的数据有：{','.join(title_list[0:7])}...")
        for news in news_list:
            pubtime = datetime.strptime(news['pubtime'], '%Y-%m-%d %H:%M:%S')
            if pubtime >= seven_days_ago:
                filtered_news.append(news)
    return filtered_news

def get_prompt(weekly_news):
    news_list = []
    for i in range(len(weekly_news)):
        news = weekly_news[i]
        each_line = f"新闻序号：{i+1} 新闻标题：{news['title']}"
        news_list.append(each_line)
    news_info = "\n".join(news_list)


    prompt = """
**任务**：从新闻报道中严格筛选出同时满足以下条件的TOP7新闻，并以列表的形式输出满足条件的新闻序号：

### 筛选标准
1. 直接或间接关联中国利益（含港澳台相关）
2. 重大国际政治/军事/外交事件
3. 显著影响全球格局
4. 地域分布最大化（覆盖≥4个大洲），尽量分布在世界不同的区域

### 筛选标准详情
1. 中国关联性（必须满足至少一项）：
   ✓ 直接提及中国/港澳台 
   ✓ 涉及中国参与的国际组织(如上合、金砖)
   ✓ 影响中国重要贸易伙伴(如俄罗斯、东盟)
   ✓ 涉及中国海外利益(一带一路项目、能源安全)

2. 全球影响维度：
   政治：改变国际联盟关系
   经济：影响全球供应链
   安全：升高区域冲突风险

3. 地域分布优化规则：
   同一大洲最多选2条，优先：
   1) 亚洲以外 2) 事件类型差异 

## 处理流程
### 第一阶段：严格过滤
1. 排除非严肃新闻（含娱乐/体育/天气）
2. 排除非事实类新闻
3. 排除未满足中国关联性的条目
4. 排除重复事件：
   - 报道的内容属于同一事件
   - 核心实体(国家/组织)重叠>80%
   - 使用文本相似度模型(threshold=0.7)

### 第二阶段：综合评分
按照筛选标准的优先级进行排序

**需要处理的新闻**：

"""  + news_info + """

**输出要求**：
1、以列表的形式输出新闻的序号
2、注意：只需要输出最终的列表即可，不需要任何多余的内容

**输出示例**：
[1, 36, 48, 102, 155, 200, 203]
"""
    return prompt
def get_llm_analysis_result(weekly_news):
    prompt = get_prompt(weekly_news)
    result = get_llm_response_with_retry(prompt)
    print('解析结果：' + result)
    match = re.search(r'\\?\[(.*?)\]', result)
    if match:
        # 返回提取到的列表字符串，并使用 eval 转换为列表
        return eval(match.group(0).replace('\\', ''))
    return []

def get_news_main(task_logs):
    news_list = get_weekly_recent_news(task_logs)
    task_logs.append("正在分析已获取新闻数据")
    news_id_list = get_llm_analysis_result(news_list)
    task_logs.append("新闻数据已分析完成")
    if not news_id_list:
        #重试
        print("重试")
        news_id_list = get_llm_analysis_result(news_list)
    if not news_id_list:
        print("重试失败")
    events = []
    for i in news_id_list:
        news = (news_list[i-1])
        url = news['url']
        title = news['title']
        print(f"正在处理：{url}")
        task_logs.append(f"正在处理：{title} - {url}")
        result = get_news_detail(news, task_logs)
        time.sleep(2)
        events.append(result)
    events.sort(key=lambda x: parse(x["time"]))
    json_path = 'news_list.json'
    save_news_list_2_json(events, json_path)
    print(json_path)
    task_logs.append(f"新闻数据已处理完成")
    return json_path, events



def get_news_detail(news, task_logs):
    news_url = news['url']
    title = news['title']
    # 详情页接口
    headers = {
        "Referer": "https://news.cctv.com/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    response = requests.get(news_url, headers=headers)
    response.encoding = 'utf-8'  # 显式设置编码
    soup = BeautifulSoup(response.text, 'html.parser')
    title_area = soup.find('div', class_='title_area').get_text(strip=True)  # 去除首尾空白
    content_area = soup.find('div', class_='content_area')
    if content_area:
        content_area = content_area.get_text(strip=True)
    else:
        if 'brief' in news:
           content_area = news['brief']
        elif 'content' in news:
            content_area = news['content']
        else:
            content_area = title
    print(title_area)
    print(content_area)
    news_context = title_area + '\n' + content_area
    task_logs.append(f"解析详情页：{title_area[0:100]}...")
    result = extract_timeline_info(title, news_context)
    print(result)
    parse_result = parse_llm_result(result)
    print(parse_result)
    return parse_result

def save_news_list_2_json(news_list, filename):
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(news_list, f, ensure_ascii=False, indent=4)



if __name__ == '__main__':
    get_news_main()