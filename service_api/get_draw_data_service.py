import json
import os
import re
import time
from datetime import datetime, timedelta

import jionlp as jio
import requests
from langchain.chains.llm import <PERSON><PERSON>hain
from langchain.chains.sql_database.query import create_sql_query_chain
from langchain_community.utilities import SQLDatabase
from langchain_core.prompts import PromptTemplate
from langchain_experimental.sql import SQLData<PERSON><PERSON>hain, SQLDatabaseSequential<PERSON>hain

from service_api.knowledge_service import KnowledgeService
from template_config.constants_config import NEWS_EVENT_KNOWLEDGE_NAME, VECTOR_INDEX_PATH_NAME, \
    VECTOR_METADATA_PATH_NAME, PARSE_RESULT_KEY_OF_TIME_SCOPE, PARSE_RESULT_KEY_OF_DATA_COUNT, \
    PARSE_RESULT_KEY_OF_SPACE_SCOPE, PARSE_RESULT_KEY_OF_DRAW_CONTENT, POSTGRESQL_INFO
from template_config.knowledge_config import PROJECT_KNOWLEDGE, TYPE_KEY, TYPE_OF_SQL_VALUE, CREATE_SQL_KEY, \
    LLM_OUTPUT_EXAMPLE_KEY, MAPPING_CONFIG_KEY, INCLUDE_TABLES
from tool.PostgreSQLDB import PostgreSQLDB
from tool.file_tool import FileUtils
from tool.llm_tool import get_llm_response_with_retry, get_not_reason_llm_response_with_retry, llm_not_reason, \
    get_not_reason_llm_response_with_retry_speed_first
from tool.sqlite_utils import SQLiteDB
from tool.vector_db_search import VectorSearch
from typing import List, Optional


class CustomPostgreSQLDB(SQLDatabase):
    """增强版PostgreSQL数据库连接，支持获取表结构注释"""

    def get_table_info(self, table_names: Optional[List[str]] = None) -> str:
        # 优先获取基础表结构
        base_info = super().get_table_info(table_names)

        # 查询扩展注释信息
        comment_query = """
        SELECT 
            col.table_name,
            col.column_name,
            col.data_type,
            (SELECT description FROM pg_description 
             WHERE objoid = t.oid AND classoid = 'pg_class'::regclass AND objsubid = 0) AS table_comment,
            (SELECT description FROM pg_description 
             WHERE objoid = t.oid AND classoid = 'pg_class'::regclass AND objsubid = col.ordinal_position) AS column_comment
        FROM information_schema.columns col
        JOIN pg_class t ON t.relname = col.table_name
        WHERE col.table_schema = 'public'
        """

        if table_names:
            # 修正：使用单引号包裹表名
            table_names_str = "', '".join(table_names)
            comment_query += f" AND col.table_name IN ('{table_names_str}')"

        try:
            comments = self._execute(comment_query)
            # 将注释信息整合到基础结构中
            return self._merge_comments(base_info, comments)
        except Exception as e:
            print(f"获取注释失败，使用基础结构: {e}")
            return base_info

    def _merge_comments(self, base_info: str, comments: List[dict]) -> str:
        """将注释信息合并到基础表结构中"""
        # 按表分组
        from collections import defaultdict
        table_comments = defaultdict(list)

        for c in comments:
            table_comments[c["table_name"]].append(c)

        result_lines = []
        for table_name, cols in table_comments.items():
            # 提取当前表的字段注释
            column_comments = {
                c["column_name"]: c["column_comment"] for c in cols
            }
            table_comment = cols[0]["table_comment"] or "无表注释"

            # 提取当前表的基础结构
            table_lines = []
            in_table = False
            for line in base_info.split('\n'):
                if line.startswith(f"CREATE TABLE {table_name} ("):
                    in_table = True
                    table_lines.append(f"CREATE TABLE {table_name} (  -- {table_comment}")
                elif in_table:
                    if line.strip().startswith(")"):
                        table_lines.append(");")
                        in_table = False
                    elif line.strip().endswith(','):
                        field = line.strip().rstrip(',')
                        field_name = field.split()[0]
                        if field_name in column_comments:
                            table_lines.append(f"  {field.ljust(30)}  -- {column_comments[field_name]}")
                        else:
                            table_lines.append(f"  {field},")
                    else:
                        table_lines.append(line)
            result_lines.extend(table_lines)

        return '\n'.join(result_lines)


class CustomSQLDatabase(SQLDatabase):
    """覆盖默认 get_table_info 方法，返回你维护的表结构定义"""

    def __init__(self, *args, custom_table_info: str = None, **kwargs):
        super().__init__(*args, **kwargs)
        self.custom_table_info = custom_table_info  # 存储你的 CREATE_SQL_KEY 内容

    def get_table_info(self, table_names: Optional[List[str]] = None) -> str:
        """强制返回你预定义的表结构信息"""
        if self.custom_table_info:
            return self.custom_table_info
        return super().get_table_info(table_names)

class GetDrawDataService:
    def __init__(self):
        pass

    @staticmethod
    def get_draw_data(semantic_subtask_dict, data_condition, task_logs):
        knowledge_name = semantic_subtask_dict['data_source'].strip()
        knowledge_info = PROJECT_KNOWLEDGE[knowledge_name]
        task_logs.append('正在获取数据....')
        time.sleep(2)
        time_str = datetime.now().strftime("%H:%M:%S")
        task_logs.append(f'{time_str} 正在处理数据....')
        if TYPE_KEY in knowledge_info and knowledge_info[TYPE_KEY] == TYPE_OF_SQL_VALUE:
            #data = GetDrawDataService.get_draw_data_with_sql_from_database_with_prompt(data_condition, knowledge_info)
            # ✅ 替换为 LangChain 实现
            #data = GetDrawDataService.get_draw_data_with_sql_from_langchain_sqlite(data_condition, knowledge_info)
            data = GetDrawDataService.get_draw_data_with_sql_from_langchain_pg(data_condition, knowledge_info)
            if not data:
                task_logs.append('根据您的输入，未找到相关数据。')
        elif 'data_source_origin_type' in knowledge_info and knowledge_info['data_source_origin_type'] == 'Network':
            task_logs.append('正在从网络获取数据....')
            data = GetDrawDataService.get_draw_data_from_network(data_condition, knowledge_info)
            task_logs.append('从网络获取数据完成')
        else:
            data = GetDrawDataService.get_event_draw_data_from_knowledge(semantic_subtask_dict, data_condition, knowledge_info)
        time_str_end = datetime.now().strftime("%H:%M:%S")
        task_logs.append(f'{time_str_end} 获取数据完成')
        return data

    @staticmethod
    def get_draw_date_time_scope(time_info):
        convert_dict = {'一周内': '最近一周', '一月内': '最近一月', '三个月内': '最近三个月',
                        '半年内': '最近半年', '一年内': '最近一年', '3天内': '最近三天', '7天内': '最近一周'}
        if time_info in convert_dict:
            time_info = convert_dict[time_info]
        # 解析时间范围
        try:
            result = jio.parse_time(time_info, time_base=datetime.now())
            if result["type"] == "time_span":
                start = result["time"][0].split(" ")[0]  # 取日期部分
                end = result["time"][1].split(" ")[0]
                return start, end
        except Exception as e:
            print(f"解析时间范围出错：{e}")
            end = time.strftime('%Y-%m-%d', time.localtime())
            start = (datetime.strptime(end, '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d')
            return start, end

    @staticmethod
    def format_news_from_rag_result(search_result):
        result = '\n'
        for index, item in enumerate(search_result):
            item_value = item
            if 'metadata' in item:
                item_value = item['metadata']
            result += '新闻' + str(index + 1) + ':\n'
            result += '    新闻发布时间：' + item_value['新闻时间'] + '\n'
            result += '    新闻来源：' + item_value['source_name'] + '\n'
            result += '    新闻标题：' + item_value['title'] + '\n'
            content = ''
            if item_value['content']:
                content = item_value['content'][0:1000]
            result += '    新闻内容：' + content + '\n\n'
        return result

    @staticmethod
    def format_query_results(results, field_mapping):
        """
        通用查询结果格式化方法

        Args:
            results: 原始查询结果集
            field_mapping: 字段映射配置（支持嵌套结构和字段合并）
               示例: {
                   "name": "airport_name",
                   "location": "region",
                   "coord": ["lat", "lng"]
               }
        Returns:
            结构化后的结果列表
        """
        formatted = []
        for row in results:
            item = {}
            for new_field, src_field in field_mapping.items():
                if isinstance(src_field, list):
                    # 处理坐标等组合字段
                    item[new_field] = [row.get(field, '无') for field in src_field]
                elif isinstance(src_field, dict):
                    field_value_concat = ''
                    # 处理嵌套字段（需实际数据支持）
                    for sub_field, sub_src_field in src_field.items():
                        field_value = row.get(sub_src_field)
                        if field_value is not None and field_value != '' and field_value != '无':
                            field_value_concat += sub_field + str(field_value) + '<br>'
                    item[new_field] = field_value_concat.rstrip('<br>')
                elif '.' in src_field:
                    # 处理嵌套字段（需实际数据支持）
                    keys = src_field.split('.')
                    value = row
                    for key in keys:
                        value = value.get(key, '无')
                        if value is None: break
                    item[new_field] = value
                else:
                    # 处理普通字段
                    item[new_field] = row.get(src_field, '无')
            formatted.append(item)
        return formatted

    @staticmethod
    def extract_sql_from_response(response_text):

        pattern = r"```sql(.*?)```"

        # 使用 re.DOTALL 确保匹配跨行内容
        matches = re.search(pattern, response_text, re.DOTALL)

        if matches:
            # 提取匹配内容并去除前后空白
            sql_code = matches.group(1).strip()
            return sql_code
        else:
            return response_text

    @staticmethod
    def get_langchain_result_sql_sqlite(data_condition, knowledge_info, max_retries=3):
        sql_chain = GetDrawDataService.create_sql_chain(knowledge_info, llm_not_reason)

        for attempt in range(max_retries):
            try:
                chain_result = sql_chain.invoke({"query": data_condition})
                sql = chain_result.get('result', '')

                print(f"第 {attempt + 1} 次生成的 SQL: {sql}")

                if sql and GetDrawDataService.validate_sqlite_sql(sql):
                    print("SQL 校验成功")
                    return sql

                print(f"SQL 校验失败，正在进行第 {attempt + 1} 次重试...")
            except Exception as e:
                print(f"第 {attempt + 1} 次调用 SQL 链失败: {e}")

        print("达到最大重试次数，未能生成合法 SQL")
        return ""

    @staticmethod
    def get_langchain_result_sql_pg(data_condition, knowledge_info, max_retries=3):
        # 使用PostgreSQL连接
        db_url = POSTGRESQL_INFO

        # 自动从数据库读取表结构
        db = CustomPostgreSQLDB.from_uri(
            db_url,
            include_tables=knowledge_info.get(INCLUDE_TABLES, []),  # 可选：指定需要包含的表
            sample_rows_in_table_info=0  # 禁用示例数据提高性能
        )

        for attempt in range(max_retries):
            try:
                table_info = db.get_table_info()
                sql_chain = GetDrawDataService.create_pg_sql_chain(knowledge_info, llm_not_reason)
                chain_result = sql_chain.invoke({
                    "dialect": "postgresql",
                    "table_info": table_info,  # 自动获取 PG 的表结构
                    "input": data_condition
                })
                sql = GetDrawDataService.extract_sql_from_response(chain_result.content)

                print(f"第 {attempt + 1} 次生成的 SQL: {sql}")

                if sql and GetDrawDataService.validate_pg_sql(sql):
                    return sql

                print(f"SQL 校验失败，正在进行第 {attempt + 1} 次重试...")
            except Exception as e:
                print(f"第 {attempt + 1} 次调用 SQL 链失败: {e}")

        print("达到最大重试次数，未能生成合法 SQL")
        return ""

    @staticmethod
    def get_draw_data_with_sql_from_langchain_sqlite(data_condition, knowledge_info):
        # 执行查询
        try:
            sql = GetDrawDataService.get_langchain_result_sql_sqlite(data_condition, knowledge_info)
            if not sql:
                return []
            raw_data = SQLiteDB.query_with_sql(sql)

            # 格式化结果（复用你的 format_query_results 方法）
            return GetDrawDataService.format_query_results(raw_data, knowledge_info[MAPPING_CONFIG_KEY])
        except Exception as e:
            print(f"LangChain SQL 生成失败: {e}")
            return []

    @staticmethod
    def get_draw_data_with_sql_from_langchain_pg(data_condition, knowledge_info):
        # 执行查询
        try:
            sql = GetDrawDataService.get_langchain_result_sql_pg(data_condition, knowledge_info)
            if not sql:
                return []
            raw_data = PostgreSQLDB.query_with_sql(sql)

            # 格式化结果（复用你的 format_query_results 方法）
            return GetDrawDataService.format_query_results(raw_data, knowledge_info[MAPPING_CONFIG_KEY])
        except Exception as e:
            print(f"LangChain SQL 生成失败: {e}")
            return []

    @staticmethod
    def create_pg_sql_chain(knowledge_info, llm):

        prompt = PromptTemplate.from_template(
            f'''
【角色设定】
资深数据库专家，擅长自然语言转SQL。您具有以下核心能力：
1. 精通关系型数据库设计，尤其熟悉PostgreSQL语法
2. 深入理解当前业务系统的表结构
3. 能准确识别业务需求中的条件，并能根据表结构信息转换成对应的查询条件

【任务说明】
您需要根据业务需求将自然语言精准转换为符合规范的SQL语句，不要执行它。

【数据库类型】
{{dialect}}

【数据库表结构】
{{table_info}}

【数据过滤条件】
{{input}}


【输入处理流程】
1. 解析数据过滤条件，提取数据查询关键信息
2. 根据提取的关键信息和表字段说明，将关键信息映射到对应的表字段，
3. 构建查询条件时，如果字段类型为Text，使用模糊匹配。注意印度和印度尼西亚这种特殊情况，在查询印度的数据时，排除印度尼西亚的数据。

【输出要求】
1. **绝对禁止**任何解释和说明
2. **必须**返回内容为可以直接执行的SQL语句，不包含任何多余的内容
3. **必须**符合ANSI SQL标准
        
        ''' + knowledge_info[LLM_OUTPUT_EXAMPLE_KEY]
        )
        return prompt | llm
        # return SQLDatabaseChain.from_llm(
        #     llm=llm,
        #     db=db,
        #     prompt=prompt,
        #     use_query_checker=False,  # 启用SQL校验
        #     verbose=False,  # 开启调试日志
        #     return_intermediate_steps=False #是否打印中间过程
        # )

    @staticmethod
    def create_sql_chain(knowledge_info, llm):
        # 1. 构建自定义数据库连接
        db_path = FileUtils.get_database_file_path()
        custom_table_info = knowledge_info[CREATE_SQL_KEY]  # 复用你的 CREATE_SQL_KEY
        db = CustomSQLDatabase.from_uri(
            f"sqlite:///{db_path}",
            custom_table_info=custom_table_info
        )
        examples = knowledge_info[LLM_OUTPUT_EXAMPLE_KEY]

        prompt = PromptTemplate.from_template(
f'''
【角色设定】
您是一位资深数据库专家，擅长根据业务需求将自然语言精准转换为符合规范的SQL语句。您具有以下核心能力：
1. 精通关系型数据库设计，尤其熟悉SQLite语法
2. 深入理解当前业务系统的表结构
3. 能准确识别业务需求中的隐藏条件

【数据库类型】
{{dialect}}

【数据库表结构】
{{table_info}}

【数据过滤条件】
{{input}}

【输入处理流程】
1. 解析数据过滤条件，提取数据查询关键信息
2. 根据提取的关键信息和表字段说明，将关键信息映射到对应的表字段
3. 构建查询条件时，如果字段类型为Text，使用模糊匹配。注意印度和印度尼西亚这种特殊情况，在查询印度的数据时，排除印度尼西亚的数据。

【输出要求】
1、只需要输出最终的sql语句，不要添加任何注释和说明信息
2、返回的SQL语句必须是符合ANSI标准的SQL语句

{examples}
'''
        )

        # 3. 创建 SQL 链（启用 LangChain 内置校验）
        return SQLDatabaseChain.from_llm(
            llm=llm,
            db=db,
            prompt=prompt,
            use_query_checker=False,  # 启用内置 SQL 校验器
            verbose=True,  # 开启调试日志
            return_intermediate_steps=False #是否打印中间过程
        )

    @staticmethod
    def get_draw_data_from_network(data_condition, knowledge_info):
        url = knowledge_info['request_url']
        response = requests.get(url)
        data = response.json()
        features = data['features']
        result_data = []
        for feature in features:
            time = feature['properties']['time']
            # 将1748907490540格式的数字转为%Y-%m-%d %H:%M:%S
            time = time / 1000
            time_str = datetime.fromtimestamp(time)
            time_str = time_str.strftime("%Y-%m-%d %H:%M:%S")

            place = feature['properties']['place']
            data = {
                'name': feature['properties']['title'],
                'location': time_str + '<br>' + place,
                'coord': [feature['geometry']['coordinates'][1], feature['geometry']['coordinates'][0]]
            }
            result_data.append(data)
        #print(result_data)
        prompt = f'''
    你的任务：根据数据过滤条件，以及给出的全球地震信息，进行过滤和转换。
    数据过滤条件：{data_condition}
    全球地震信息：{result_data}
    处理流程：
    1. 根据用户输入的要求，过滤地震信息。
    2. 将过滤后的数据中的name和location字段转为简体中文，请注意location中的<br>字符为html换行符，不需要转为中文，但是其他的信息需要转换成简体中文。
    
    输出要求：
    1. 只输出过滤后的地震信息，不需要输出其他内容。
    2. 输出的格式为json，包含name、location和coord字段。
    3. 只需要返回json数据，不需要其他的解释说明

'''
        draw_data = get_not_reason_llm_response_with_retry(prompt)
        print(draw_data)
        if draw_data.startswith('```json'):
            draw_data = draw_data[7:]
        if draw_data.endswith('```'):
            draw_data = draw_data[:-3]
        draw_data = json.loads(draw_data)
        return draw_data

    @staticmethod
    def validate_sqlite_sql(sql):
        try:
            SQLiteDB.query_with_sql(f"EXPLAIN QUERY PLAN {sql}")
            return True
        except Exception as e:
            print(f"SQL 校验失败: {e}")
            return False

    @staticmethod
    def validate_pg_sql(sql: str) -> bool:
        """
        使用 PostgreSQL 的 EXPLAIN 命令验证 SQL 语法是否正确

        Args:
            sql (str): 待验证的 SQL 语句

        Returns:
            bool: 验证是否通过
        """
        if not sql or not sql.strip():
            print("SQL 为空，验证失败")
            return False

        try:
            # 使用 EXPLAIN ANALYZE 检查 SQL 语法
            result = PostgreSQLDB.query_with_sql(f"EXPLAIN ANALYZE {sql}")
            if result:
                print("SQL 校验成功")
                return True
            else:
                print("SQL 校验返回空结果")
                return False
        except Exception as e:
            print(f"SQL 校验失败: {e}")
            return False

    @staticmethod
    def get_draw_data_with_sql_from_database_with_prompt(data_condition, knowledge_info, max_retries=3):
        sql = ""
        for attempt in range(max_retries):
            sql = GetDrawDataService.get_query_sql_with_llm(data_condition, knowledge_info)
            if GetDrawDataService.validate_sqlite_sql(sql):
                break
            else:
                print(f"SQL:{sql} 校验失败，重试第 {attempt + 1} 次")
                if attempt == max_retries - 1:
                    print("Max retries reached. Exiting.")
                    return []
        query_result = SQLiteDB.query_with_sql(sql)
        print(f"{sql}的查询结果数目为: {len(query_result)}")
        formatted_data = GetDrawDataService.format_query_results(query_result, knowledge_info[MAPPING_CONFIG_KEY])
        if formatted_data:
            print(f'得到结构化数据{len(formatted_data)}条')
        #print(formatted_data)
        return formatted_data
    @staticmethod
    def get_query_sql_with_llm(data_condition, knowledge_info):
        prompt = f'''
【角色设定】
您是一位资深数据库专家，擅长根据业务需求将自然语言精准转换为符合规范的SQL语句。您具有以下核心能力：
1. 精通关系型数据库设计，尤其熟悉SQLite数据库语法
2. 深入理解当前业务系统的表结构
{knowledge_info[CREATE_SQL_KEY]}
3. 能准确识别业务需求中的隐藏条件

【数据过滤条件】
数据过滤条件：{data_condition}

【输入处理流程】
1. 解析数据过滤条件，提取数据查询关键信息
2. 根据提取的关键信息和表字段说明，将关键信息映射到对应的表字段
3. 构建查询条件时：
   - 对Text字段（国家/省份/城市）使用模糊匹配，如城市匹配北京时，需使用city LIKE '北京%'
   - 处理多模糊条件时使用 OR 连接多个 LIKE 条件
   - 排除特殊情况（如印度需排除印度尼西亚）
   
【输出要求】
1、只需要输出最终的sql语句，不要添加任何注释和说明信息
2、返回的SQL语句必须是符合ANSI标准的SQL语句

{knowledge_info[LLM_OUTPUT_EXAMPLE_KEY]}
'''
        sql = get_not_reason_llm_response_with_retry(prompt)
        #print(sql)
        return sql

    @staticmethod
    def get_event_draw_data_from_knowledge(semantic_subtask_dict, data_condition, knowledge_info):
        filters = []
        if PARSE_RESULT_KEY_OF_TIME_SCOPE in data_condition:
            time_info = data_condition.get(PARSE_RESULT_KEY_OF_TIME_SCOPE)
            start_date, end_date = GetDrawDataService.get_draw_date_time_scope(time_info)
            print(f'时间范围：{start_date} - {end_date}')
            filters = [
                {
                    'field': '新闻时间',
                    'op': '>=',
                    'value': start_date
                },
                {
                    'field': '新闻时间',
                    'op': '<=',
                    'value': end_date
                }
            ]
        filters.append({
            'field': 'source_name',
            'op': 'in',  # 或 'in' 如果允许多个来源
            'value': ["新华国际头条", "人民日报", "观察者网", "环球时报","环球网", "CHINADAILY", "国际在线", "人民日报国际", "参考消息"]
        })
        index_path = KnowledgeService.get_knowledge_path(knowledge_info, VECTOR_INDEX_PATH_NAME)
        meta_file_path = KnowledgeService.get_knowledge_path(knowledge_info, VECTOR_METADATA_PATH_NAME)
        search_str = GetDrawDataService.get_knowledge_search_str(semantic_subtask_dict, data_condition)
        search_result = VectorSearch(index_path, meta_file_path).filtered_search(search_str, filters, 20)[0]
        #过滤掉字符超过2000的文章
        search_result = [item for item in search_result if len(item['metadata']['combined_text']) <= 2000]
        print(f'筛选后新闻列表数量：{len(search_result)}')
        draw_data = GetDrawDataService.get_event_data_from_news_result(search_result)
        draw_data = GetDrawDataService.get_filter_data_from_news_event_result(semantic_subtask_dict, data_condition, draw_data)
        return draw_data

    @staticmethod
    def get_knowledge_search_str(semantic_subtask_dict, data_condition):
        result = semantic_subtask_dict.get('title') + '\n' + str(data_condition)
        return result


    @staticmethod
    def get_event_data_from_news_result(search_result_list):
        event_news = GetDrawDataService.format_news_from_rag_result(search_result_list)
        ##重要事件定义##：
        ##对世界和国家影响重大的、严肃的事件，如外交政策、政治事件，地缘冲突，军事冲突，经济政策等。

        prompt = f'''
##任务##：
提取出给出的新闻列表中的所有事件信息。
处理流程如下：
1、依次对给出的新闻内容进行处理，提取出每个新闻里面的全部事件信息。请注意，有的新闻中会存在多个事件，其中的每个事件都需要输出。
2、将给出的所有新闻中提取出的所有事件进行汇总输出。


##输出要求##：
1. 每个事件独立成行，格式为：
时间：yyyy-mm-dd；地点：国家，城市；内容：20字内摘要
2. 字段规则：
- 时间：事件发生的时间，格式为yyyy-mm-dd，例如2025-04-17。如果新闻没有明确时间，则获取新闻时间
- 地点：事件具体发生的地点，格式为城市名称，例如美国，洛杉矶。如果无法知道出具体城市信息，则返回该地的行政中心地址
- 内容：事件内容的简短摘要，不超过20字
3. 只需要输出提取出的事件，不需要说明和解释内容。

##输出示例：
1、时间：2025-04-17；地点：塞尔维亚，贝尔格莱德；内容：马楚特出任塞尔维亚总理
2、时间：2025-04-07；地点：美国，加州；内容：加州宣布起诉特朗普政府关税政策


##新闻内容##：
{event_news}
        

'''
        print(prompt[:1000])
        result = get_not_reason_llm_response_with_retry_speed_first(prompt)
        print(result)
        return result

    @staticmethod
    def get_filter_data_from_news_event_result(semantic_subtask_dict, data_condition, news_result):
        space_scope = data_condition[PARSE_RESULT_KEY_OF_SPACE_SCOPE]
        event_count = data_condition[PARSE_RESULT_KEY_OF_DATA_COUNT]
        prompt = f'''
##任务##：
请根据数据过滤条件从给出的新闻列表中，按照如下流程进行事件提取，按指定格式返回满足用户输入的事件：
1. 先过滤掉和标绘内容完全无关的新闻事件
2. 再过滤掉重复的事件，描述的是同一件事情，则认为是重复事件。
4. 请注意：用户需要的发生地点在{space_scope}范围内的事件。
3. 按照事件的重要性和影响程度，提取出前{event_count}条的事件，如果满足条件的事件不足{event_count}条，则获取实际满足条件的事件。
4. 如果空间范围为全球或者大洲，同一个国家的数据不要过3条！！！
5、每个事件的格式为严格的JSON格式， 包含以下字段：
        - time（必填）
        - location（必填）
        - coord（必填）
        - content（必填）
6、每个事件的JSON字段要求如下：
    - time字段为事件发生的时间，格式为yyyy-mm-dd，例如2025-04-17
    - location字段为事件发生的地点，格式为城市名称，例如美国，洛杉矶。
    - coord字段为事件发生的经纬度，格式为[纬度, 经度]，例如[34.0522, -118.2437]，如果地点为范围，则给出中心点经纬度。请确保华盛顿的经纬度坐标值正确！！
    - content字段为事件的简短内容概况，不超过20字。
7、每个事件的location和coord信息保持一致，coord的值对应的时location的经纬度坐标
8、按照输出示例返回JSON格式，不要输出```json等内容。
9、输出的内容只需要包含JSON格式，不需要解释和说明。

##输出示例##：
[
{{
    "time": "2025-04-17",
    "location": "塞尔维亚，贝尔格莱德",
    "coord": [44.82, 20.46],
    "content": "马楚特正式出任塞尔维亚总理"
}},
{{
    "time": "2025-04-07",
    "location": "中国，北京",
    "coord": [39.90, 116.40],
    "content": "肯尼亚总统应邀访华"
}},
    ]

##标绘内容##：
{semantic_subtask_dict.get('title', '')}

##数据过滤条件##：
{data_condition}

##新闻列表##：
{news_result}

'''
        print(prompt)
        result = get_not_reason_llm_response_with_retry(prompt)
        if result.startswith('```json'):
            result = result[7:]
        if result.endswith('```'):
            result = result[:-3]
        if result.startswith('[]'):
            return '[]'
        print(result)
        return result


    @staticmethod
    def get_knowledge_name(user_input, parse_result):
        default_return_value = NEWS_EVENT_KNOWLEDGE_NAME
        knowledge_info = KnowledgeService.get_knowledge_info_str()


        prompt = f'''
请根据用户的输入，解析出来的标绘信息，并结合给出的知识库信息，返回最适合用户需求的知识库名称。
##用户输入##：
{user_input}

##解析出来的标绘信息##：
{parse_result}

##知识库信息##:
{knowledge_info}

##输出要求##：
1、返回最适合的知识库名称，如果都不合适，则输出{default_return_value}。
2、你只需要输出最终的知识库名称，不需要任何多余的内容

##输出示例##：
{default_return_value}
        '''
        #print(prompt)
        result = get_llm_response_with_retry(prompt)
        parse_result = KnowledgeService.parse_parse_knowledge_result(result, default_return_value)
        print(parse_result)
        return parse_result


    @staticmethod
    def get_event_draw_data_from_local(user_input, parse_result):
        # 获取当前文件所在路径
        file_path = os.path.abspath(__file__)
        file_path = os.path.dirname(file_path)
        json_path = os.path.join(os.path.dirname(file_path), 'data', 'global_news.json')
        json_data = json.load(open(json_path, 'r', encoding='utf-8'))
        data_count = parse_result.get(PARSE_RESULT_KEY_OF_DATA_COUNT, 12)
        prompt = f'''
    请根据用户的输入，以及解析出来的标绘信息，从给出的数据中提取出符合用户要求的数据，并输出为JSON格式：
    ##用户输入##：
    {user_input}
    ##解析出来的标绘信息##：
    {parse_result}
    
    ##处理流程##：
    1、先根据用户输入的地理范围和事件范围对给出的原始数据进行过滤
    2、对过滤后的数据进行筛选：返回结果中最多包含{data_count}条数据。如果满足条件过滤后的数据不足{data_count}，则返回所有满足条件的数据。如果满足条件的数据超过{data_count}条，则按照对世界的影响性、与中国的关联性、各个区域均匀发布的原则，返回前{data_count}条数据。


    输出要求：
    1、输出的内容必须只包含最终的JSON内容，不要输出其他多余的任何内容。
    2、JSON数据中的每条数据必须和给出的原始数据严格一致！
    3、如果没有满足条件的数据，请输出空数组。

    输出示例：
    [
    {{
        "time": "2025-04-17",
        "location": "塞尔维亚，贝尔格莱德",
        "coord": [44.82, 20.46],
        "content": "马楚特正式出任塞尔维亚总理"
      }}
    ]

    给出的原始数据:
    {json_data}
    '''
        print(prompt)
        result = get_llm_response_with_retry(prompt)

        print('解析结果：' + result)
        return result

if __name__ == '__main__':
    result = jio.parse_time('最近7天', time_base=datetime.now())
    print(result)
    result = jio.parse_time('2025-04-09 到 2025-04-18', time_base=datetime.now())
    print(result)
    sql = "SELECT * FROM tb_airport WHERE country IN ('中国', '俄罗斯', '蒙古', '哈萨克斯坦', '吉尔吉斯斯坦', '塔吉克斯坦', '乌兹别克斯坦', '土库曼斯坦', '越南', '老挝', '柬埔寨', '泰国', '马来西亚', '新加坡', '印度尼西亚', '菲律宾', '文莱', '缅甸', '孟加拉国', '斯里兰卡', '马尔代夫', '尼泊尔', '不丹', '巴基斯坦', '印度', '伊朗', '伊拉克', '土耳其', '叙利亚', '约旦', '黎巴嫩', '以色列', '巴勒斯坦', '沙特阿拉伯', '也门', '阿曼', '阿联酋', '卡塔尔', '科威特', '巴林', '希腊', '塞浦路斯', '埃及', '阿尔巴尼亚', '北马其顿', '塞尔维亚', '黑山', '波黑', '克罗地亚', '斯洛文尼亚', '罗马尼亚', '保加利亚', '匈牙利', '斯洛伐克', '捷克', '波兰', '立陶宛', '拉脱维亚', '爱沙尼亚', '白俄罗斯', '乌克兰', '摩尔多瓦' AND city LIKE '%首都%'"
    ss = GetDrawDataService.validate_pg_sql(sql)
    print(ss)
    sss = GetDrawDataService.get_draw_data_with_sql_from_database_with_prompt({}, {})