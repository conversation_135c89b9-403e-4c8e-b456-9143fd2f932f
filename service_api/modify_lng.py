
import json

def adjust_longitude(coords):
    """仅处理经度（每个坐标的第一个元素）"""
    if isinstance(coords, list):
        # 识别坐标点 [经度, 纬度, ...]
        if len(coords) == 2 and all(isinstance(x, (float, float)) for x in coords[:2]):
            # 只处理经度，保持纬度和其他值不变
            lon = coords[0]
            adjusted_lon = lon + 360 if lon < -20 else lon
            return [adjusted_lon] + coords[1:]
        else:
            # 递归处理嵌套结构
            return [adjust_longitude(item) for item in coords]
    return coords

#
# # 读取原始文件
# with open(r'D:\vue_project\time_line_ui\time_line_ui\public\maps\global-data\global_polygon.geojson', 'r', encoding='utf-8') as f:
#     geojson = json.load(f)
#
# # 处理所有坐标
# for feature in geojson['features']:
#     geometry = feature['geometry']
#     geometry['coordinates'] = adjust_longitude(geometry['coordinates'])  # 关键修改点
# # 保存结果
# with open('adjusted.geojson', 'w', encoding='utf-8') as f:
#     json.dump(geojson, f )

# with open(r'map.json', 'r', encoding='utf-8') as f:
#     geojson = json.load(f)
#     result = []
#     for each_data in geojson['countries']:
#         each_result = {}
#         each_result['center'] = each_data['center']
#         each_result['iso3'] = each_data['iso3']
#         each_result['iso2'] = each_data['iso2']
#         result.append(each_result)
with open('map_result.json', 'r', encoding='utf-8') as f:
    geojson = json.load(f)

result = {}
for each_data in geojson:
    result[each_data['iso2']] = each_data

with open('map_result_adjust.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, indent=4)