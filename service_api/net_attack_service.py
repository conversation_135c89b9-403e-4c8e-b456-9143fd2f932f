#最流行的七个免费网络威胁地图
#https://shuyeidc.com/wp/126632.html
#https://livethreatmap.radware.com/
import json
import os.path
import random
from datetime import datetime, timezone, timedelta

import requests

json_path = os.path.join(os.path.dirname(__file__), 'map_result_adjust.json')
map_result_json = json.loads(open(json_path, 'r', encoding='utf-8').read())

def trans_datetime(att_time):
    time_parts = att_time.split('.', 1)  # 分割第一个小数点
    main_time = time_parts[0]  # 获取主要时间部分

    # 步骤2：解析为无时区datetime对象
    dt = datetime.strptime(main_time, "%Y-%m-%dT%H:%M:%S")

    # 步骤3：假设原始时间为UTC时间
    utc_time = dt.replace(tzinfo=timezone.utc)

    # 步骤4：转换为UTC+8时区
    utc8_time = utc_time.astimezone(timezone(timedelta(hours=8)))

    return utc8_time.strftime("%Y-%m-%d %H:%M:%S")

def get_attact_data():
    response = requests.get('https://ltm-prod-api.radware.com/map/attacks?limit=10')
    result = []
    for item in response.json():
        if type(item) == list:
           for each_item in item:
               sourceCountry = each_item['sourceCountry']
               destinationCountry = each_item['destinationCountry']
               att_type = each_item['type']
               att_time = each_item['attackTime']
               if sourceCountry and destinationCountry and sourceCountry.strip() and destinationCountry.strip():
                   if sourceCountry in map_result_json and destinationCountry in map_result_json:
                       source_center = map_result_json[sourceCountry]['center']
                       des_center = map_result_json[destinationCountry]['center']
                       if source_center[0] < -20:
                           source_center[0] = source_center[0] + 360
                       if des_center[0] < -20:
                           des_center[0] = des_center[0] + 360
                       result.append({
                           'sourceCountry': sourceCountry,
                           'sourceCountry_center': source_center,
                           'destinationCountry': destinationCountry,
                           'destinationCountry_center': des_center,
                           'type': att_type,
                           'attackTime': trans_datetime(att_time),
                           'lon': source_center[0],
                           'lat': source_center[1],
                           'lon2': des_center[0],
                           'lat2': des_center[1],
                       })

    type_groups = {}
    for item in result:
        att_type = item['type']
        type_groups.setdefault(att_type, []).append(item)

    final_result = []
    for i in range(0, 100):
        for att_type, items in type_groups.items():
            if len(items) > i:
                item = items[i]
                final_result.append(item)

    count = random.randint(66, 120)

    return final_result[:count]  # 确保不超过100条


if __name__ == '__main__':
    print(get_attact_data())
