import json
import os
from datetime import datetime

from service_api.get_draw_data_service import GetDrawDataService
from service_api.icon_service import IconService
from template_config import constants_config
from template_config.constants_config import TASK_TYPE_OF_EVENT_DRAW, TASK_TYPE_OF_NET_ATTACK
from template_config.user_instruction_config import get_user_instruction_parse_prompt
from tool.llm_tool import get_llm_response_with_retry, parse_json_result_2_dict, get_not_reason_llm_response_with_retry


class UserInputParser:
    def __init__(self, task_logs):
        self.work_type_list = ["全球一周热点事件标绘", "网络攻击", "其他"]
        self.task_logs = task_logs


    def get_work_type(self, work_content):
        prompt  = f'''
    请对用户的输入进行解析，并返回对应的工作类型。
    用户输入：{work_content}
    
    以下是工作类型：{self.work_type_list}
    
    输出要求：
    1、返回对应的其中一个工作类型，不要输出其他内容。
    2、返回的工作类型必须是上述列表中的内容。
    
    输出示例：
    全球一周热点事件标绘
    
    '''
        response = get_llm_response_with_retry(prompt)
        print('解析结果：' + response)
        return response

    #
    # def execute_user_input_with_instructions(self, user_input):
    #     instruction_result = self.get_user_instruction_parse_result(user_input)
    #     instruction_result = parse_json_result_2_dict(instruction_result)
    #     draw_data = GetDrawDataService.get_draw_data(user_input, instruction_result, self.task_logs)
    #     if draw_data:
    #         draw_data_dict = self.convert_data_2_dict(draw_data)
    #         if instruction_result.get(constants_config.PARSE_RESULT_KEY_OF_TASK_TYPE) != TASK_TYPE_OF_NET_ATTACK:
    #             self.task_logs.append(f"数据条数{len(draw_data_dict)}")
    #         instruction_result[constants_config.PARSE_RESULT_KEY_OF_DRAW_DATA] = draw_data_dict
    #         time_str = datetime.now().strftime("%H:%M:%S")
    #         self.task_logs.append(f"{time_str} 开始进行数据格式转换...")
    #         draw_data_with_icon = IconService.add_icon_info_for_draw_data(user_input, instruction_result, draw_data)
    #         ui_result = self.convert_parse_result_2_ui_format(user_input, instruction_result, draw_data_with_icon)
    #         time_str_end = datetime.now().strftime("%H:%M:%S")
    #         self.task_logs.append(f"{time_str_end} 数据格式转换完成！")
    #         print(instruction_result)
    #         return ui_result
    #     else:
    #         return []
    #
    # def parse_user_input(self, user_input):
    #     parse_result = {}
    #     # 将用户输入解析为场景信息
    #     scene_info = self.parse_user_input_2_scene_info(user_input)
    #     for key, value in parse_json_result_2_dict(scene_info).items():
    #         parse_result[key] = value
    #     # 将场景信息解析为语义信息
    #     semantics_info = self.parse_scene_2_semantics(user_input, scene_info)
    #     for key, value in parse_json_result_2_dict(semantics_info).items():
    #         parse_result[key] = value
    #     print(parse_result)
    #     # 获取标绘数据
    #     draw_data = GetDrawDataService.get_draw_data(user_input, parse_result, self.task_logs)
    #     draw_data_dict = self.convert_data_2_dict(draw_data)
    #     self.task_logs.append(f"数据条数{len(draw_data_dict)}")
    #     time_str = datetime.now().strftime("%H:%M:%S")
    #     self.task_logs.append(f"{time_str} 开始进行数据格式转换...")
    #     draw_data_with_icon = IconService.add_icon_info_for_draw_data(user_input, parse_result, draw_data)
    #     ui_result = self.convert_parse_result_2_ui_format(user_input, parse_result, draw_data_with_icon)
    #     time_str_end = datetime.now().strftime("%H:%M:%S")
    #     self.task_logs.append(f"{time_str_end} 数据格式转换完成！")
    #     return ui_result

    def convert_data_2_dict(self, draw_data):
        if type(draw_data) == str:
            if draw_data.startswith('```json'):
                draw_data = draw_data[7:]
            if draw_data.endswith('```'):
                draw_data = draw_data[:-3]
            return json.loads(draw_data)
        else:
            return draw_data

    def parse_user_input_2_scene_info(self, user_input: str):
        time_str = datetime.now().strftime("%H:%M:%S")
        self.task_logs.append(f"{time_str} 开始对用户输入：{user_input} 进行场景解析...")
        prompt = f'''
    请根据用户的输入，解析出用户想要进行的地图标绘信息。
    用户输入：{user_input}
    
    输出要求：
    1、输出的内容必须只包含最终的JSON内容，不要输出其他多余的任何内容。
    2、JSON字符串中必须包含以下字段：
        - {constants_config.PARSE_RESULT_KEY_OF_TASK_TYPE}（必填）
        - {constants_config.PARSE_RESULT_KEY_OF_DRAW_CONTENT}（必填）
        - {constants_config.PARSE_RESULT_KEY_OF_TIME_SCOPE}（必填）
        - {constants_config.PARSE_RESULT_KEY_OF_SPACE_SCOPE}（必填）
        - {constants_config.PARSE_RESULT_KEY_OF_DATA_COUNT}（必填）
    3、各字段解释如下：
        {constants_config.PARSE_RESULT_KEY_OF_TASK_TYPE}：
            取值范围：{constants_config.TASK_TYPE_OF_EVENT_DRAW}、{constants_config.TASK_TYPE_OF_NOT_EVENT_DRAW}、{constants_config.TASK_TYPE_OF_NET_ATTACK}、{constants_config.TASK_TYPE_OF_OTHERS}。
            新闻、热点、事件、大事、动态等等都属于{constants_config.TASK_TYPE_OF_EVENT_DRAW}。机场、港口等非事件类的数据属于{constants_config.TASK_TYPE_OF_NOT_EVENT_DRAW}。默认{constants_config.TASK_TYPE_OF_EVENT_DRAW}。
        {constants_config.PARSE_RESULT_KEY_OF_DRAW_CONTENT}：
            需要标绘的内容，例如：全球热点事件、欧洲经济数据、中国台湾重大事件 等。
        {constants_config.PARSE_RESULT_KEY_OF_TIME_SCOPE}：
            标注内容的时间范围，比如最近一周、最近三天、2023-01-01 到 2023-01-07 等，默认值为最近一周。
        {constants_config.PARSE_RESULT_KEY_OF_SPACE_SCOPE}：
            根据用户的输入，判断需要在地图的哪个范围进行标绘，可以是全球、跨国级、国家级、行政区划级等，比如中国台湾、欧洲、美国、日本、亚太地区 等，默认值为全球。
            如果用户需要的是航母、航母母舰、军舰等活动范围为全球的内容，该值为全球
        {constants_config.PARSE_RESULT_KEY_OF_DATA_COUNT}：
            数据类型为整型,表示需要在地图上标绘的数据条数，默认值为10。
    输出示例：
    {{
        "{constants_config.PARSE_RESULT_KEY_OF_TASK_TYPE}": "{constants_config.TASK_TYPE_OF_EVENT_DRAW}",
        "{constants_config.PARSE_RESULT_KEY_OF_DRAW_CONTENT}": "全球一周热点事件",
        "{constants_config.PARSE_RESULT_KEY_OF_TIME_SCOPE}": "2025-04-09 到 2025-04-18",
        "{constants_config.PARSE_RESULT_KEY_OF_SPACE_SCOPE}": "中国台湾",
        "{constants_config.PARSE_RESULT_KEY_OF_DATA_COUNT}": 12
    }}
    
    '''
        scene_result = get_not_reason_llm_response_with_retry(prompt)
        print('解析结果：' + scene_result)
        time_str_end = datetime.now().strftime("%H:%M:%S")
        self.task_logs.append(f"{time_str_end} 场景解析结果完成")
        return scene_result



    def parse_scene_2_semantics(self, user_input, scene_result):
        prompt = f'''
    请结合用户的原始输入，以及解析出来的标绘场景信息，按照下面的要求，解析出该标绘场景最适合的{constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}，{constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER}，{constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER_LNG_LAT}，{constants_config.PARSE_RESULT_KEY_OF_MAP_ZOOM}。
    ##用户输入##：
    {user_input}
    ##解析出来的标绘场景信息##：
    {scene_result}
    
    字段解释：
    - {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：从可选底图列表中，选择最适合用户标绘场景的{constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}，默认为事件标绘底图，注意：返回{constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}。
        --可选底图列表：
        {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：网络攻击底图        适合场景：网络攻击
        {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：事件标绘底图        适合场景：全球、事件标绘
        {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：中国标绘底图        适合场景：中国、事件标绘
        {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：影像底图        适合场景：全球、影像场景
    - {constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER}：地图的视觉中心点，如果是空间范围为全球，返回太平洋，如果是洲，返回洲中心点，如果是空间范围为国家，返回国家中心点，如果是空间范围为行政区划，返回行政区划中心点，默认为太平洋。
    - {constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER_LNG_LAT}：地图的视觉中心点的经纬度坐标，格式为[lng, lat]，例如[155, 15], [0, 0]，默认返回[155, 15]。
    - {constants_config.PARSE_RESULT_KEY_OF_MAP_ZOOM}：地图可视化框架中的zoom参数，需根据用户要标绘的空间范围来自适应缩放级别，要求标绘空间占据地图主体的80%，默认值为4。如果空间范围为欧洲，则值为6。
    解析要求：
    1、输出的内容必须只包含最终的JSON内容，不要输出其他多余的任何内容。
    2、JSON字符串中必须包含以下字段：
        - {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}（必填）
        - {constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER}（必填）
        - {constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER_LNG_LAT}（必填）
        - {constants_config.PARSE_RESULT_KEY_OF_MAP_ZOOM}（必填）
    
    输出示例：
    {{
        "{constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}": "事件标绘底图",
        "{constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER}": "太平洋",
        "{constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER_LNG_LAT}": [155, 15],
        "{constants_config.PARSE_RESULT_KEY_OF_MAP_ZOOM}": 4
    }}
    
    '''
        time_str = datetime.now().strftime("%H:%M:%S")
        self.task_logs.append(f"{time_str} 正在对用户输入：{user_input} 进行语义解析，该过程耗时较长，请耐心等待...")
        semantics_result = get_llm_response_with_retry(prompt)
        print('解析结果：' + semantics_result)
        time_str_end = datetime.now().strftime("%H:%M:%S")
        self.task_logs.append(f"{time_str_end} 语义解析完成")
        return semantics_result

    def convert_parse_result_2_ui_format(self, user_input, parse_result, draw_data):
        ui_format = {}
        if constants_config.PARSE_RESULT_KEY_OF_SPACE_SCOPE in parse_result:
            ui_format['SPACE'] = parse_result[constants_config.PARSE_RESULT_KEY_OF_SPACE_SCOPE]
        if constants_config.PARSE_RESULT_KEY_OF_TASK_TYPE in parse_result:
            ui_format['TYPE'] = parse_result[constants_config.PARSE_RESULT_KEY_OF_TASK_TYPE]
            if ui_format['TYPE'] == TASK_TYPE_OF_EVENT_DRAW:
                ui_format['NEED_TIME_LINE'] = '是'
        if constants_config.PARSE_RESULT_KEY_OF_MAP_NAME in parse_result:
            ui_format['MAP'] = parse_result[constants_config.PARSE_RESULT_KEY_OF_MAP_NAME]
        if constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER_LNG_LAT in parse_result:
            ui_format['MAP_CENTER'] = parse_result[constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER_LNG_LAT]
        if constants_config.PARSE_RESULT_KEY_OF_MAP_ZOOM in parse_result:
            ui_format['ZOOM'] = parse_result[constants_config.PARSE_RESULT_KEY_OF_MAP_ZOOM]
        if constants_config.PARSE_RESULT_KEY_OF_DRAW_CONTENT in parse_result:
            ui_format['TITLE'] = parse_result[constants_config.PARSE_RESULT_KEY_OF_DRAW_CONTENT]
        ui_format['DRAW_DATA'] = parse_json_result_2_dict(draw_data)
        self.format_ui_data(ui_format)
        print(ui_format)
        return ui_format




    def format_ui_data(self,ui_format_data):
        if  ui_format_data['SPACE'] :
            if ui_format_data['SPACE'] == '全球':
                for key, value in ui_format_data.items():
                    if key == 'DRAW_DATA':
                        draw_data = ui_format_data[key]
                        for each_data in draw_data:
                            coord = each_data['coord']
                            lng = coord[1]
                            if type(lng) == str:
                                lng = float(lng)
                            if lng < -20:
                                coord[1] = lng + 360
                    elif key == 'MAP_CENTER':
                        lng = ui_format_data[key][0]
                        if type(lng) == str:
                            lng = float(lng)
                        if lng < -20:
                            ui_format_data[key][0] = lng + 360
            else:
                for key, value in ui_format_data.items():
                    if key == 'DRAW_DATA':
                        draw_data = ui_format_data[key]
                        for each_data in draw_data:
                            coord = each_data['coord']
                            lng = coord[1]
                            if lng > 180:
                                coord[1] = lng - 360
                    elif key == 'MAP_CENTER':
                        lng = ui_format_data[key][0]
                        if type(lng) == str:
                            lng = float(lng)
                        if lng > 180:
                            ui_format_data[key][0] = lng - 360

    def get_user_instruction_parse_result(self, user_input):
        time_str = datetime.now().strftime("%H:%M:%S")
        self.task_logs.append(f"{time_str} 正在对用户输入：{user_input} 进行解析...")
        prompt = get_user_instruction_parse_prompt(user_input)
        result = get_not_reason_llm_response_with_retry(prompt)
        time_str_end = datetime.now().strftime("%H:%M:%S")
        self.task_logs.append(f"{time_str_end} 用户输入解析完成！")
        print(result)
        return result


if __name__ == '__main__':
    par = UserInputParser([])

    par.get_user_instruction_parse_result("全球热点事件标绘")
    par.get_user_instruction_parse_result("美国机场数据")
    par.get_user_instruction_parse_result("中国西安机场数据")
