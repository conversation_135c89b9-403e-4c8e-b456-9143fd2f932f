from template_config import constants_config
from template_config.constants_config import PARSE_RESULT_KEY_OF_TASK_TYPE, PARSE_RESULT_KEY_OF_DRAW_CONTENT, \
    PARSE_RESULT_KEY_OF_TIME_SCOPE, PARSE_RESULT_KEY_OF_SPACE_SCOPE, PARSE_RESULT_KEY_OF_DATA_COUNT, \
    PARSE_RESULT_KEY_OF_MAP_NAME, PARSE_RESULT_KEY_OF_MAP_CENTER, PARSE_RESULT_KEY_OF_MAP_CENTER_LNG_LAT, \
    PARSE_RESULT_KEY_OF_MAP_ZOOM, PARSE_RESULT_KEY_OF_KNOWLEDGE_NAME, PARSE_RESULT_KEY_OF_ICON_PATH, \
    PARSE_RESULT_KEY_OF_DRAW_DATA
from tool.llm_tool import get_not_reason_llm_response_with_retry

USER_INSTRUCTION_CONFIG = {
PARSE_RESULT_KEY_OF_TASK_TYPE:  "",     # 任务类型
PARSE_RESULT_KEY_OF_DRAW_CONTENT:  "",  #  标绘内容
PARSE_RESULT_KEY_OF_TIME_SCOPE: "",     #  时间范围
PARSE_RESULT_KEY_OF_SPACE_SCOPE:  "",   #  空间范围
PARSE_RESULT_KEY_OF_DATA_COUNT: "",     #  数据条数
PARSE_RESULT_KEY_OF_MAP_NAME: "",       #  底图名称
PARSE_RESULT_KEY_OF_MAP_CENTER: "",     #  地图中心点
PARSE_RESULT_KEY_OF_MAP_CENTER_LNG_LAT: "",   #  中心点经纬度坐标
PARSE_RESULT_KEY_OF_MAP_ZOOM: "",           #  地图缩放级别
PARSE_RESULT_KEY_OF_KNOWLEDGE_NAME : "",    #  知识库名称
PARSE_RESULT_KEY_OF_ICON_PATH : "",         #  "图标路径"
PARSE_RESULT_KEY_OF_DRAW_DATA : [],         #  "标绘数据"
}



def get_user_instruction_parse_prompt(user_input) -> str:
    prompt = f'''
你是一名专业的资深地图绘制师，请对用户的输入进行解析，并将解析的结果按要求填入模板：

##用户输入##：
{user_input}

##模板##：
{USER_INSTRUCTION_CONFIG}

##模板字段说明##：
-{constants_config.PARSE_RESULT_KEY_OF_TASK_TYPE}：
    取值范围：{constants_config.TASK_TYPE_OF_EVENT_DRAW}、{constants_config.TASK_TYPE_OF_NOT_EVENT_DRAW}、{constants_config.TASK_TYPE_OF_NET_ATTACK}、{constants_config.TASK_TYPE_OF_OTHERS}。
    新闻、热点、事件、大事、动态等等都属于{constants_config.TASK_TYPE_OF_EVENT_DRAW}。
    机场、港口、地震、水电站等非事件类的数据属于{constants_config.TASK_TYPE_OF_NOT_EVENT_DRAW}。
    默认{constants_config.TASK_TYPE_OF_EVENT_DRAW}。
-{constants_config.PARSE_RESULT_KEY_OF_DRAW_CONTENT}：
    需要标绘的内容，例如：全球热点事件、欧洲经济数据、美国德克萨斯州机场数据 等。
-{constants_config.PARSE_RESULT_KEY_OF_TIME_SCOPE}：
    标注内容的时间范围，比如最近一周、最近三天、2023-01-01 到 2023-01-07 等，默认值为最近一周。
-{constants_config.PARSE_RESULT_KEY_OF_SPACE_SCOPE}：
    需要在地图的哪个范围进行标绘，可以是全球、跨国级、国家级、行政区划级等，比如中国台湾、欧洲、美国、日本、亚太地区 等，默认值为全球。
    如果用户需要的是航母、航母母舰、军舰等活动范围为全球的内容，该值为全球
-{constants_config.PARSE_RESULT_KEY_OF_DATA_COUNT}：
    在地图上标绘的数据条数，默认值为10。
- {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：从可选底图列表中，选择最适合用户标绘场景的{constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}，默认为事件标绘底图，注意：返回{constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}。
    --可选底图列表：
    {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：网络攻击底图        适合场景：网络攻击，该底图为网络攻击场景专用底图
    {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：事件标绘底图        适合场景：标注空间为全球、事件标绘、浅色底图
    {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：中国标绘底图        适合场景：适用中国相关场景
    {constants_config.PARSE_RESULT_KEY_OF_MAP_NAME}：影像底图        适合场景：影像场景
- {constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER}：地图的视觉中心点，如果是空间范围为全球，返回太平洋，如果是洲，返回洲中心点，如果是空间范围为国家，返回国家中心点，如果是空间范围为行政区划，返回行政区划中心点，默认为太平洋。
- {constants_config.PARSE_RESULT_KEY_OF_MAP_CENTER_LNG_LAT}：地图的视觉中心点的经纬度坐标，格式为[经度, 纬度]，例如[155, 15], [0, 0]，默认返回[155, 15]。
- {constants_config.PARSE_RESULT_KEY_OF_MAP_ZOOM}：
    --地图可视化框架的zoom参数根据空间范围层级化设定：
    全球范围：默认缩放级别为4
    大洲级（如亚洲/非洲）：缩放级别5
    次大陆级（如南亚/北欧）：缩放级别6
    国家/地区级（如法国/日本）：缩放级别7
    省级/城市群级（如加州/长三角）：缩放级别8
    城市级（如巴黎/东京）：缩放级别9
    --特殊规则：
    欧洲大陆整体视为次大陆级（zoom=6）
    微型国家（面积<5万km²）自动提升一级
    标绘空间自动适配至占据地图视窗75-85%
    未知区域采用机器学习模型预测最佳缩放级别
    默认为zoom=4。
 
##输出要求##：
1、未说明的模板字段也需要输出，按照模板默认值输出。
2、严格按照模板字段说明进行输出，不要输出多余的字段。
3、输出结果为严格的JSON格式，不需要额外的解释，不需要输出```json等字样。

    '''
    return prompt



if __name__ == '__main__':
    print()