import os.path

from template_config.constants_config import NEWS_EVENT_KNOWLEDGE_NAME, VECTOR_INDEX_PATH_NAME, \
    VECTOR_METADATA_PATH_NAME, ICON_KNOWLEDGE_NAME, AIRPORTS_KNOWLEDGE_NAME, CRUISEPORTS_KNOWLEDGE_NAME, \
    HYDRO_KNOWLEDGE_NAME, EARTHQUAKE_KNOWLEDGE_NAME

EVENT_INDEX_PATH = os.path.join('news_knowledge', 'news_index.index')
EVENT_META_PATH = os.path.join('news_knowledge', 'news_metadata.parquet')


AIRPORTS_INDEX_PATH = os.path.join('airport_knowledge', "airports_index.index")
AIRPORTS_META_PATH = os.path.join('airport_knowledge', "airports_metadata.parquet")

CRUISEPORTS_INDEX_PATH = os.path.join('cruiseport_knowledge', "cruiseports_index.index")
CRUISEPORTS_META_PATH = os.path.join('cruiseport_knowledge', "cruiseports_metadata.parquet")

TYPE_KEY = 'type'
CREATE_SQL_KEY = 'create_sql'
LLM_OUTPUT_EXAMPLE_KEY = 'llm_output_example'
MAPPING_CONFIG_KEY = 'mapping_config'
KNOWLEDGE_DESCRIBE_KEY = '知识库描述'
KNOWLEDGE_SCENES_KEY = '使用场景'
TYPE_OF_SQL_VALUE = 'SQL'
INCLUDE_TABLES = 'include_tables'
TYPE_OF_VECTOR_VALUE = '向量数据库'

PROJECT_KNOWLEDGE = {
    NEWS_EVENT_KNOWLEDGE_NAME : {
        TYPE_KEY: TYPE_OF_VECTOR_VALUE,
        KNOWLEDGE_DESCRIBE_KEY: '该知识库内容为从多个新闻网站获取的新闻报道',
        KNOWLEDGE_SCENES_KEY: '获取新闻事件，热点事件，事件动态，重大事件，网络攻击等',
        VECTOR_INDEX_PATH_NAME: EVENT_INDEX_PATH,
        VECTOR_METADATA_PATH_NAME: EVENT_META_PATH
    },
    AIRPORTS_KNOWLEDGE_NAME : {
        KNOWLEDGE_DESCRIBE_KEY: '该知识库内容为全球机场数据',
        KNOWLEDGE_SCENES_KEY: '用于获取机场数据和信息',
        TYPE_KEY: TYPE_OF_SQL_VALUE,
        INCLUDE_TABLES: ['tb_airport'],
        CREATE_SQL_KEY: """
        CREATE TABLE tb_airport (
            "UID" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,    -- 主键自增ID
            "SCALE_RANK" INTEGER,                                -- 原始保留字段，无实际用途
            "WIKIPEDIA" TEXT,                                    -- 维基百科链接
            "NALTSCALE" INTEGER,                                 -- 原始保留字段，无实际用途
            "WIKIDATAID" TEXT,                                   -- 维基数据ID
            "WDID_SCORE" INTEGER,                                -- 原始保留字段，无实际用途
            "DETAIL_INFO" TEXT,                                  -- 机场详细信息 
            "NAME_CH" TEXT,                                      -- 机场中文名称 
            "NAME_EN" TEXT,                                      -- 机场英文名称
            "CONTINENT" TEXT,                                    -- 机场所在大洲，取值范围[亚洲, 欧洲, 北美洲, 南美洲, 大洋洲, 非洲]。字段作用：用于根据洲名称来过滤数据
            "COUNTRY" TEXT,                                      -- 机场所在国家，简体中文名称，如中国, 美国, 英国等。字段作用：用于根据国家名称来过滤数据
            "PROVINCE_STATE" TEXT,                               -- 机场所在省份或州或邦，简体中文名称，如陕西省, 德克萨斯州, 北方邦等。字段作用：用于根据所在省、州、邦等名称过滤数据
            "CITY" TEXT,                                         -- 机场所在城市，简体中文名称，如北京市, 达拉斯, 莫斯科等。字段作用：用于根据城市名过滤数据
            "LONGITUDE" FLOAT,                                   -- 机场所在经度
            "LATITUDE" FLOAT,                                    -- 机场所在纬度
            "LOCATION_DESCRIBE" TEXT,                            -- 机场所在位置描述，如中国陕西省西安市临潼区
            "ALTITUDE" FLOAT,                                    -- 机场海拔高度，单位为米
            "AIRPORT_TYPE" TEXT,                                 -- 机场类型，取值范围[民用, 军民两用, 军用]，其中军民两用机场既可以军用，也可以民用，如果是过滤军用机场，则同时需过滤出军民两用的机场。
            "IATA_CODE" TEXT,                                    -- IATA机场代码
            "ICAO_CODE" TEXT,                                    -- ICAO机场代码
            "AIRPORT_SCALE" TEXT,                                -- 机场规模，取值范围[小型, 中型, 大型]
            "OPERATION_INFO" TEXT                                -- 机场运营信息
        )
        """,
        LLM_OUTPUT_EXAMPLE_KEY: '''
【示例1】
用户问题："标绘美国德克萨斯州机场数据"
输出：
SELECT * FROM tb_airport WHERE country like '美国%' AND province_state like '德克萨斯%'

【示例2】
用户问题："标绘美国加州机场数据"
输出：
SELECT * FROM tb_airport WHERE country like '美国%' AND province_state like '加利福利亚%'

【示例3】
用户问题："标绘美国西部沿海机场数据"
输出：
SELECT * FROM tb_airport WHERE country like '美国%' AND (province_state like '加利福尼亚%' OR province_state like '俄勒冈%' OR province_state like '华盛顿%')
        ''',
        MAPPING_CONFIG_KEY: {
            "name": "NAME_CH".lower(),       # 直接字段映射
            "location": "LOCATION_DESCRIBE".lower(),         # 字段重命名
            "coord": ["LATITUDE".lower(), "LONGITUDE".lower()]       # 多字段合并
        }
    },
    CRUISEPORTS_KNOWLEDGE_NAME: {
        KNOWLEDGE_DESCRIBE_KEY: '该知识库内容为全球港口数据',
        KNOWLEDGE_SCENES_KEY: '用于获取港口数据和信息',
        TYPE_KEY: TYPE_OF_SQL_VALUE,
        INCLUDE_TABLES: ['tb_cruiseport'],
        CREATE_SQL_KEY: """
        CREATE TABLE tb_cruiseport (
            "UID" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,       -- 主键自增ID
            "NAME_CH" TEXT,                                         -- 港口中文名称
            "NAME_EN" TEXT,                                         -- 港口英文名称
            "LOCATION_DESCRIBE" TEXT,                               -- 港口所在位置描述
            "FACILITIES" TEXT,                                      -- 港口设施 
            "PORT_CODE" TEXT,                                       -- 港口代码
            "INTRODUCTION" TEXT,                                    -- 港口介绍
            "CONTINENT" TEXT,                                       -- 港口所在大洲，取值范围[亚洲, 欧洲, 北美洲, 南美洲, 大洋洲, 非洲, 南极洲]。字段作用：用于根据洲名称来过滤数据
            "COUNTRY" TEXT,                                         -- 港口所在国家，简体中文名称，如中国, 美国, 英国等。字段作用：用于根据国家名称来过滤数据
            "PROVINCE_STATE" TEXT,                                   -- 港口所在省份或州或邦，简体中文名称，如陕西省, 德克萨斯州, 北方邦等。字段作用：用于根据所在省、州、邦等名称过滤数据
            "CITY" TEXT,                                             -- 港口所在城市，简体中文名称，如西安市, 达拉斯, 莫斯科等。字段作用：用于根据城市名过滤数据
            "LONGITUDE" FLOAT,                                       -- 港口所在经度
            "LATITUDE" FLOAT,                                        -- 港口所在纬度
            "FUNCTION_TYPE" TEXT,                                     -- 港口功能类型，取值范围[商港, 渔港, 工业港, 军港, 避风港, 旅游港]
            "LOCATION_TYPE" TEXT,                                    -- 港口位置类型，取值范围[海港, 河港, 河口港, 湖港, 水库港]
            "TIDAL_INFLUENCE_TYPE" TEXT,                             -- 港口受潮汐影响类型，取值范围[开敞港, 闭合港, 混合港]
            "FREEZING_TYPE" TEXT,                                    -- 港口是否冻结类型，取值范围[冻港, 不冻港]
            "SCALE" TEXT                                            -- 港口规模，取值范围[特大型港口, 大型港口, 中型港口, 小型港口]
        )
        """,
        LLM_OUTPUT_EXAMPLE_KEY: '''
【示例1】
用户问题："标绘美国德克萨斯州港口数据"
输出：
SELECT * FROM tb_cruiseport WHERE country like '美国%' AND province_state like '德克萨斯%'

【示例2】
用户问题："标绘美国加州港口数据"
输出：
SELECT * FROM tb_cruiseport WHERE country like '美国%' AND province_state like '加利福利亚%'

【示例3】
用户问题："标绘美国西部沿海港口数据"
输出：
SELECT * FROM tb_cruiseport WHERE country like '美国%' AND (province_state like '加利福尼亚%' OR province_state like '俄勒冈%' OR province_state like '华盛顿%')
        ''',
        MAPPING_CONFIG_KEY: {
            "name": "NAME_CH".lower(),       # 直接字段映射
            "location": "LOCATION_DESCRIBE".lower(),         # 字段重命名
            "coord": ["LATITUDE".lower(), "LONGITUDE".lower()]       # 多字段合并
        }
    },
    HYDRO_KNOWLEDGE_NAME: {
        KNOWLEDGE_DESCRIBE_KEY: '该知识库内容为全球水电站和大坝数据',
        KNOWLEDGE_SCENES_KEY: '用于获取水电站和大坝数据和信息',
        TYPE_KEY: TYPE_OF_SQL_VALUE,
        INCLUDE_TABLES: ['tb_hydro_data'],
        CREATE_SQL_KEY: """
        CREATE TABLE tb_hydro_data (
            "ID" TEXT NOT NULL,                 -- 主键
            country TEXT,                       -- 水电站所在国家简体中文名称，如中国, 美国, 英国等。字段作用：用于根据国家名称来过滤数据
            name_ch TEXT,                       -- 水电站中文名称
            name_en TEXT,                       -- 水电站英文名称
            capacity_mw FLOAT,                  -- 水电站装机容量，单位：兆瓦 (MW)
            plant_lat FLOAT,                    -- 水电站所在纬度
            plant_lon FLOAT,                    -- 水电站所在经度
            plant_type TEXT,                    -- 水电站类型，取值范围[径流式, 蓄水式, 抽水蓄能式, 运河式]
            year INTEGER,                        -- 水电站投运年份，如2001
            dam_name TEXT,                       -- 大坝中文名称
            dam_height_m FLOAT,                   -- 大坝高度，单位：米 (m)
            res_name TEXT,                       -- 水库中文名称
            man_dam_lat FLOAT,                   -- 大坝所在纬度
            man_dam_lon FLOAT,                   -- 大坝所在经度
            river TEXT,                          -- 所在河流中文名称
            head_m FLOAT,                        -- 水电站水头，指水电站实际可利用的水位差（单位：米）
            res_avg_depth_m FLOAT,               -- 水库平均深度，单位：米 (m) 
            res_area_km2 FLOAT,                  -- 水库面积，单位：平方公里 (km2)
            res_vol_km3 FLOAT,                  -- 水库容积，单位：立方米 (km3)
            continent TEXT,                      -- 水电站所在大洲，取值范围[亚洲, 欧洲, 北美洲, 南美洲, 大洋洲, 非洲, 南极洲]。字段作用：用于根据洲名称来过滤数据
            province_state TEXT,                 -- 水电站所在省份或州或邦，简体中文名称，如陕西省, 德克萨斯州, 北方邦等。字段作用：用于根据所在省、州、邦等名称过滤数据
            city TEXT,                           -- 水电站所在城市，简体中文名称，如西安市, 达拉斯, 莫斯科等。字段作用：用于根据城市名过滤数据
            PRIMARY KEY ("ID")
        )
        """,
        LLM_OUTPUT_EXAMPLE_KEY: '''
【示例1】
用户问题："标绘美国德克萨斯州水电站数据"
输出：
SELECT * FROM tb_hydro_data WHERE country like '美国%' AND province_state like '德克萨斯%'

【示例2】
用户问题："标绘美国加州水电站数据"
输出：
SELECT * FROM tb_hydro_data WHERE country like '美国%' AND province_state like '加利福利亚%'

【示例3】
用户问题："标绘美国西部沿海水电站数据"
输出：
SELECT * FROM tb_hydro_data WHERE country like '美国%' AND (province_state like '加利福尼亚%' OR province_state like '俄勒冈%' OR province_state like '华盛顿%')
        ''',
        MAPPING_CONFIG_KEY: {
            "name": "name_ch",       # 直接字段映射
            "location": {'水电站类型：': 'plant_type', '装机容量（兆瓦）：': 'capacity_mw', '大坝名称：': 'dam_name'},         # 字段重命名
            "coord": ["plant_lat", "plant_lon"]       # 多字段合并
        }
    },
    EARTHQUAKE_KNOWLEDGE_NAME: {
        'data_source_origin_type': 'Network',
        'request_url': 'https://earthquake.usgs.gov/earthquakes/feed/v1.0/summary/2.5_day.geojson',
        'data_source_type': 'GeoJson',
    }
}


ICONS_INDEX_PATH = os.path.join('icons_knowledge', "icons_index.index")
ICONS_META_PATH = os.path.join('icons_knowledge', "icons_metadata.parquet")

ICON_KNOWLEDGE = {
    ICON_KNOWLEDGE_NAME : {
        VECTOR_INDEX_PATH_NAME: ICONS_INDEX_PATH,
        VECTOR_METADATA_PATH_NAME: ICONS_META_PATH
    }
}







