import json
import os
import shutil
import subprocess
import threading

import uuid
from datetime import datetime

import uvicorn
from fastapi import FastAPI, Body
from starlette.middleware.cors import CORSMiddleware

from service_api.net_attack_service import get_attact_data

app = FastAPI()

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], # 精确指定前端地址
    allow_credentials=True,
    allow_methods=["*"], # 允许所有HTTP方法
    allow_headers=["*"], # 允许所有头
)

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/graph")
async def debug(file_path: str):
    print(file_path)
    file_name = os.path.basename(file_path)
    des_path = os.path.join(os.getcwd(), 'ragtest', 'input')
    out_put_path = os.path.join(os.getcwd(), 'ragtest', 'output')
    cache_path = os.path.join(os.getcwd(), 'ragtest', 'cache')
    if os.path.exists(out_put_path):
        shutil.rmtree(out_put_path)
    if os.path.exists(cache_path):
        shutil.rmtree(cache_path)
    print(os.path.exists(des_path))
    if os.path.exists(des_path):
        shutil.rmtree(des_path)
    if not os.path.exists(des_path):
        os.makedirs(des_path)
    file_path = shutil.copy2(file_path, des_path)
    try:
        result = subprocess.run(
            ["graphrag", "index", "--root", "./ragtest"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace'
        ).stdout
        print(result)
    except Exception as e:
        print(f"命令执行失败: {str(e)}")
        return None
    if 'All workflows completed successfully.' in result:
        print('执行成功')
        result_file = r'D:\python_project\NewsEventLine\ragtest\output\graph.graphml'
        if os.path.exists(result_file):
            result_file_name = file_name.replace('.txt', '.graphml')
            result = shutil.copy2(result_file, os.path.join('D:\\vue_project\\time_line_ui\\time_line_ui\\public\\data', result_file_name))
            print(result)
        return result_file
    else:
        print('执行失败')
    return None

tasks = {}

@app.post("/work_process")
async def create_task(work_content: str = Body(..., embed=True)):

    task_id = str(uuid.uuid4())
    time_str = datetime.now().strftime("%H:%M:%S")
    tasks[task_id] = {
        "logs": [f"{time_str} 任务开始处理..."],
        "completed": False
    }


    processing_thread = threading.Thread(
        target=process_task_with_parse,
        args=(task_id, work_content)
    )
    processing_thread.start()

    return {"task_id": task_id}


@app.get("/work_progress/{task_id}")
def get_progress(task_id: str):

    task = tasks.get(task_id, {})
    if not task:
        return {"error": "任务不存在"}
    return task

@app.get("/attack-data")
def get_attack():
    return get_attact_data()


def process_task_with_parse(task_id: str, user_input: str):
    try:
        task = tasks.get(task_id, {})
        task_logs = task.get("logs", [])

        task_logs.append(f"✅ 开始 解析用户输入: {user_input}")

        # 初始化 LangGraphAgent
        from langgraph_agent.main_graph import LangGraphAgent
        agent = LangGraphAgent()
        # 执行 Agent 流程
        agent_result = agent.invoke(user_input, task_logs)
        # print(agent_result)
        parse_result = agent_result["ui_final_result"]
        # print(parse_result)

        # parse = UserInputParser(task_logs)
        # parse_result = parse.execute_user_input_with_instructions(user_input)
        #parse_result = parse.parse_user_input(user_input)
        # 调用 Agent 处理用户输入
        # _executor = get_agent_executor(task_logs)
        # agent_result = _executor.invoke({
        #     "input": user_input,
        #     "chat_history": []
        # })
        # print(agent_result)
        #
        # final_data = {
        #     # 保留原始解析结果字段
        #     "work_type": agent_result.get("work_type", ""),
        #     "time_range": agent_result.get("time_range", ""),
        #
        #     # 新增 Agent 处理结果
        #     "agent_steps": [
        #         {
        #             "tool": step[0].tool,
        #             "input": step[0].tool_input,
        #             "output": step[1]
        #         } for step in agent_result.get("intermediate_steps", [])
        #     ],
        #
        #     # 最终 UI 数据（从最后一步获取）
        #     "ui_data": None
        # }
        # print(final_data)
        # last_step = agent_result["intermediate_steps"][len(agent_result.get("intermediate_steps", [])) - 1]
        # ui_result_data = last_step[1]  # parse_draw_data_2_ui_format 的输出
        # if type(ui_result_data) == str:
        #     ui_result_data = json.loads(ui_result_data)
        # final_data["ui_data"] = ui_result_data


        tasks[task_id]["completed"] = True
        tasks[task_id]["data"] = parse_result

    except Exception as e:
        #打印堆栈
        import traceback
        traceback.print_exc()
        #tasks[task_id]["logs"].append(f"❌ 发生错误：{str(e)}")
        tasks[task_id]["completed"] = True
        tasks[task_id]["logs"].append(f"连接中断，请重试")




if __name__ == "__main__":


    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=False)

